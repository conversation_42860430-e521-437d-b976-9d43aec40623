"""
Enhanced KPI Views that eliminate manual entry and enforce enterprise automation.
These views replace manual KPI management with automated calculation and monitoring.
"""

from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.db.models import Q
from datetime import datetime, timedelta
from decimal import Decimal
import logging

from .models import KPI, KPIValue, KPICategory, Employee
from .serializers import KPISerializer, KPIValueSerializer, KPICategorySerializer
from .enhanced_kpi_engine import enhanced_kpi_engine
from .kpi_monitoring import kpi_monitor
from .hierarchical_access import get_hierarchical_access_manager
from .enhanced_permissions import HierarchicalKPIPermission, EnhancedRoleBasedPermission

logger = logging.getLogger(__name__)


class EnhancedKPIViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Enhanced KPI ViewSet with hierarchical access control and enterprise automation.

    Key Features:
    - Read-only access to KPIs (no manual creation/editing)
    - Hierarchical role-based data filtering
    - Manager-subordinate access patterns
    - Automatic calculation triggers
    - Real-time monitoring and alerts
    - Data quality reporting
    """
    queryset = KPI.objects.filter(status='ACTIVE')
    serializer_class = KPISerializer
    permission_classes = []  # Allow unauthenticated access for dashboard viewing

    def get_queryset(self):
        """Filter KPIs based on hierarchical access control and specific filters"""
        try:
            access_manager = get_hierarchical_access_manager(self.request.user)
            queryset = super().get_queryset()

            # Apply hierarchical filtering
            filtered_queryset = access_manager.filter_kpi_queryset(queryset)

            # Apply specific KPI filtering if provided
            specific_kpis = self.request.query_params.get('specific_kpis')
            if specific_kpis:
                kpi_names = [name.strip() for name in specific_kpis.split(',')]
                filtered_queryset = filtered_queryset.filter(name__in=kpi_names)
                logger.info(f"Filtering by specific KPIs: {kpi_names}")

            # Apply KPI type filtering if provided
            kpi_type = self.request.query_params.get('kpi_type')
            if kpi_type:
                logger.info(f"Filtering by KPI type: {kpi_type}")

            logger.info(f"User {self.request.user.username} ({access_manager.role_name}) "
                       f"accessing {filtered_queryset.count()} KPIs out of {queryset.count()} total")

            return filtered_queryset

        except Exception as e:
            logger.error(f"Error filtering KPI queryset for user {self.request.user.username}: {str(e)}")
            return KPI.objects.none()
    
    @action(detail=True, methods=['post'])
    def recalculate(self, request, pk=None):
        """
        Trigger recalculation of a specific KPI.
        This replaces manual value entry with automatic calculation.
        """
        kpi = self.get_object()
        
        if not kpi.is_automated:
            return Response(
                {'error': 'This KPI is not configured for automatic calculation'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Get date range from request or use current month
            start_date = request.data.get('start_date')
            end_date = request.data.get('end_date')
            
            if start_date:
                start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            else:
                start_date = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
            if end_date:
                end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            else:
                end_date = timezone.now()
            
            # Calculate KPI value
            calculated_value = enhanced_kpi_engine.calculate_kpi_with_cache(kpi, start_date, end_date)
            
            if calculated_value is not None:
                # Update KPI current value
                kpi.current_value = calculated_value
                kpi.last_updated = timezone.now()
                kpi.save(update_fields=['current_value', 'last_updated'])
                
                # Create KPI value record
                kpi_value, created = KPIValue.objects.update_or_create(
                    kpi=kpi,
                    period_start=start_date,
                    period_end=end_date,
                    defaults={
                        'value': calculated_value,
                        'recorded_by': Employee.objects.get(user=request.user),
                        'is_estimated': False,
                        'confidence_level': Decimal('100.0'),
                        'data_quality_score': Decimal('100.0'),
                        'notes': 'Manually triggered automatic calculation',
                        'source_data': {
                            'calculation_method': kpi.calculation_method,
                            'engine': 'EnhancedKPICalculationEngine',
                            'timestamp': timezone.now().isoformat(),
                            'triggered_by': request.user.username
                        }
                    }
                )
                
                return Response({
                    'success': True,
                    'kpi_id': kpi.id,
                    'kpi_name': kpi.name,
                    'calculated_value': float(calculated_value),
                    'period_start': start_date.isoformat(),
                    'period_end': end_date.isoformat(),
                    'created_new_value': created
                })
            else:
                return Response(
                    {'error': 'Failed to calculate KPI value. Check data availability.'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            return Response(
                {'error': f'Calculation failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['post'])
    def recalculate_all(self, request):
        """
        Trigger recalculation of all automated KPIs.
        This ensures all KPIs are up-to-date with latest operational data.
        """
        try:
            # Get date range from request or use current month
            start_date = request.data.get('start_date')
            end_date = request.data.get('end_date')
            
            if start_date:
                start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            if end_date:
                end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            
            # Run enhanced calculation
            results = enhanced_kpi_engine.calculate_all_kpis_enhanced(start_date, end_date)
            
            return Response({
                'success': True,
                'results': results,
                'message': f'Calculated {results["calculated"]} KPIs successfully'
            })
            
        except Exception as e:
            return Response(
                {'error': f'Batch calculation failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def data_quality_report(self, request):
        """
        Get data quality report for all KPIs.
        This helps identify KPIs with potential data issues.
        """
        try:
            active_kpis = KPI.objects.filter(status='ACTIVE', is_automated=True)
            
            report = {
                'total_kpis': active_kpis.count(),
                'kpis_with_current_values': 0,
                'kpis_without_values': 0,
                'average_data_quality_score': 0.0,
                'kpis_needing_attention': [],
                'last_updated_summary': {},
                'generated_at': timezone.now().isoformat()
            }
            
            total_quality_score = 0
            kpis_with_scores = 0
            
            for kpi in active_kpis:
                if kpi.current_value:
                    report['kpis_with_current_values'] += 1
                    
                    # Get latest KPI value for data quality score
                    latest_value = kpi.values.first()
                    if latest_value and latest_value.data_quality_score:
                        total_quality_score += float(latest_value.data_quality_score)
                        kpis_with_scores += 1
                        
                        # Flag KPIs with low data quality
                        if latest_value.data_quality_score < 80:
                            report['kpis_needing_attention'].append({
                                'kpi_id': kpi.id,
                                'kpi_name': kpi.name,
                                'data_quality_score': float(latest_value.data_quality_score),
                                'last_updated': kpi.last_updated.isoformat() if kpi.last_updated else None,
                                'issue': 'Low data quality score'
                            })
                    
                    # Check for stale data
                    if kpi.last_updated:
                        days_since_update = (timezone.now() - kpi.last_updated).days
                        if days_since_update > 7:
                            report['kpis_needing_attention'].append({
                                'kpi_id': kpi.id,
                                'kpi_name': kpi.name,
                                'last_updated': kpi.last_updated.isoformat(),
                                'days_since_update': days_since_update,
                                'issue': 'Stale data (not updated in 7+ days)'
                            })
                else:
                    report['kpis_without_values'] += 1
                    report['kpis_needing_attention'].append({
                        'kpi_id': kpi.id,
                        'kpi_name': kpi.name,
                        'issue': 'No current value available'
                    })
            
            # Calculate average data quality score
            if kpis_with_scores > 0:
                report['average_data_quality_score'] = total_quality_score / kpis_with_scores
            
            # Last updated summary
            now = timezone.now()
            report['last_updated_summary'] = {
                'within_24_hours': active_kpis.filter(
                    last_updated__gte=now - timedelta(hours=24)
                ).count(),
                'within_week': active_kpis.filter(
                    last_updated__gte=now - timedelta(days=7)
                ).count(),
                'older_than_week': active_kpis.filter(
                    last_updated__lt=now - timedelta(days=7)
                ).count(),
                'never_updated': active_kpis.filter(last_updated__isnull=True).count()
            }
            
            return Response(report)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to generate data quality report: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def monitoring_status(self, request):
        """
        Get KPI monitoring status including alerts and health summary.
        """
        try:
            # Get KPI health summary
            health_summary = kpi_monitor.get_kpi_health_summary()
            
            # Get recent alerts
            alert_results = kpi_monitor.check_all_kpis()
            
            return Response({
                'health_summary': health_summary,
                'recent_alerts': alert_results,
                'monitoring_active': True,
                'last_check': timezone.now().isoformat()
            })
            
        except Exception as e:
            return Response(
                {'error': f'Failed to get monitoring status: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def hierarchical_access_info(self, request):
        """
        Get hierarchical access information for the current user.
        Shows what data the user can access based on organizational hierarchy.
        """
        try:
            access_manager = get_hierarchical_access_manager(request.user)

            # Get accessible entities
            accessible_employees = access_manager.get_accessible_employees()
            accessible_departments = access_manager.get_accessible_departments()
            accessible_projects = access_manager.get_accessible_projects()

            # Get hierarchical path
            hierarchical_path = access_manager.get_hierarchical_path()

            # Get role-specific KPI categories
            kpi_categories = access_manager.get_role_specific_kpi_categories()

            access_info = {
                'user_role': access_manager.role_name,
                'hierarchical_path': hierarchical_path,
                'accessible_data': {
                    'employees_count': accessible_employees.count(),
                    'departments_count': accessible_departments.count(),
                    'projects_count': accessible_projects.count(),
                    'employees': [
                        {
                            'id': emp.id,
                            'name': emp.user.get_full_name(),
                            'position': emp.position,
                            'department': emp.department.name if emp.department else None
                        }
                        for emp in accessible_employees[:10]  # Limit to first 10 for performance
                    ],
                    'departments': [
                        {
                            'id': dept.id,
                            'name': dept.name,
                            'manager': dept.manager.user.get_full_name() if dept.manager else None
                        }
                        for dept in accessible_departments
                    ],
                    'projects': [
                        {
                            'id': proj.id,
                            'name': proj.name,
                            'manager': proj.project_manager.user.get_full_name() if proj.project_manager else None
                        }
                        for proj in accessible_projects[:10]  # Limit to first 10 for performance
                    ]
                },
                'kpi_categories': kpi_categories,
                'access_summary': {
                    'can_access_all_data': access_manager.role_name in ['SUPERADMIN', 'ADMIN'],
                    'has_hierarchical_access': access_manager.role_name in ['DEPARTMENT_MANAGER', 'PROJECT_MANAGER'],
                    'is_manager': bool(accessible_employees.count() > 1),
                    'department_scoped': access_manager.role_name == 'DEPARTMENT_MANAGER',
                    'project_scoped': access_manager.role_name == 'PROJECT_MANAGER'
                }
            }

            return Response({
                'status': 'success',
                'data': access_info,
                'message': 'Hierarchical access information retrieved successfully'
            })

        except Exception as e:
            logger.error(f"Error getting hierarchical access info: {str(e)}")
            return Response(
                {'error': f'Failed to get hierarchical access info: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class EnhancedKPIValueViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Enhanced KPI Value ViewSet with hierarchical access control.

    Key Features:
    - Read-only to prevent manual entry
    - Hierarchical data filtering based on organizational structure
    - Manager-subordinate data access patterns
    - Department and project scoped data access
    - All KPI values automatically calculated from operational data
    """
    queryset = KPIValue.objects.all()
    serializer_class = KPIValueSerializer
    permission_classes = []  # Allow unauthenticated access for dashboard viewing

    def get_queryset(self):
        """Filter KPI values based on hierarchical access control and data scoping"""
        try:
            access_manager = get_hierarchical_access_manager(self.request.user)
            queryset = super().get_queryset()

            # Apply hierarchical filtering for KPI values
            filtered_queryset = access_manager.filter_kpi_values_queryset(queryset)

            logger.info(f"User {self.request.user.username} ({access_manager.role_name}) "
                       f"accessing {filtered_queryset.count()} KPI values")

            return filtered_queryset.select_related('kpi', 'recorded_by')

        except Exception as e:
            logger.error(f"Error filtering KPI values queryset for user {self.request.user.username}: {str(e)}")
            return KPIValue.objects.none()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def enhanced_kpi_dashboard(request):
    """
    Enhanced KPI dashboard that shows only automatically calculated KPIs.
    This replaces the manual KPI dashboard with enterprise-grade automation.
    """
    try:
        # Get active automated KPIs
        kpis = KPI.objects.filter(status='ACTIVE', is_automated=True).select_related('category')
        
        # Filter by user permissions
        user_profile = getattr(request.user, 'userprofile', None)
        if user_profile and user_profile.role:
            kpis = kpis.filter(
                Q(visible_to_roles=user_profile.role) |
                Q(visible_to_roles__isnull=True)
            )
        
        # Categorize KPIs
        dashboard_data = {
            'total_kpis': kpis.count(),
            'automated_kpis': kpis.count(),  # All KPIs are automated
            'manual_kpis': 0,  # No manual KPIs allowed
            'categories': [],
            'top_performing_kpis': [],
            'underperforming_kpis': [],
            'recent_updates': [],
            'data_quality_summary': {},
            'automation_status': 'FULLY_AUTOMATED'
        }
        
        # Get categories with KPI counts
        categories = KPICategory.objects.filter(kpis__in=kpis).distinct()
        for category in categories:
            category_kpis = kpis.filter(category=category)
            dashboard_data['categories'].append({
                'id': category.id,
                'name': category.name,
                'name_ar': category.name_ar,
                'kpi_count': category_kpis.count(),
                'category_type': category.name  # Fixed: use name instead of category_type
            })
        
        # Get top performing KPIs (those meeting or exceeding targets)
        top_performing = []
        underperforming = []
        
        for kpi in kpis:
            if kpi.current_value and kpi.target_value:
                achievement_ratio = float(kpi.current_value) / float(kpi.target_value)
                
                kpi_data = {
                    'id': kpi.id,
                    'name': kpi.name,
                    'name_ar': kpi.name_ar,
                    'current_value': float(kpi.current_value),
                    'target_value': float(kpi.target_value),
                    'achievement_ratio': achievement_ratio,
                    'measurement_type': kpi.measurement_type,
                    'unit': kpi.unit,
                    'trend_direction': kpi.trend_direction,
                    'last_updated': kpi.last_updated.isoformat() if kpi.last_updated else None
                }
                
                if (kpi.trend_direction == 'UP' and achievement_ratio >= 0.9) or \
                   (kpi.trend_direction == 'DOWN' and achievement_ratio <= 1.1):
                    top_performing.append(kpi_data)
                else:
                    underperforming.append(kpi_data)
        
        # Sort by achievement ratio
        top_performing.sort(key=lambda x: x['achievement_ratio'], reverse=True)
        underperforming.sort(key=lambda x: x['achievement_ratio'])
        
        dashboard_data['top_performing_kpis'] = top_performing[:10]
        dashboard_data['underperforming_kpis'] = underperforming[:10]
        
        # Get recent updates
        recent_kpis = kpis.filter(last_updated__isnull=False).order_by('-last_updated')[:5]
        dashboard_data['recent_updates'] = [
            {
                'id': kpi.id,
                'name': kpi.name,
                'current_value': float(kpi.current_value) if kpi.current_value else None,
                'last_updated': kpi.last_updated.isoformat(),
                'calculation_method': kpi.calculation_method
            }
            for kpi in recent_kpis
        ]
        
        # Data quality summary
        total_with_values = kpis.filter(current_value__isnull=False).count()
        dashboard_data['data_quality_summary'] = {
            'kpis_with_values': total_with_values,
            'kpis_without_values': dashboard_data['total_kpis'] - total_with_values,
            'data_completeness_percentage': (total_with_values / dashboard_data['total_kpis'] * 100) if dashboard_data['total_kpis'] > 0 else 0
        }
        
        return Response(dashboard_data)
        
    except Exception as e:
        return Response(
            {'error': f'Failed to load enhanced KPI dashboard: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
