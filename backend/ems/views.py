from rest_framework import viewsets, status, filters
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth.models import User
from .permissions import (
    RoleBasedPermission,
    EmployeePermission,
    HRManagerPermission,
    AdminPermission,
    EmployeeDataPermission,
    IsOwnerOrReadOnly
)
from django.db.models import Q, Sum, Count, Avg, F
from django.db import models
from django.utils import timezone
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator
from django.core.cache import cache
from .models import (
    Department, Employee, Activity, Role, UserProfile, LeaveType, LeaveRequest,
    Attendance, Project, Task, Budget, Expense, AssetCategory, Asset, Supplier,
    PurchaseOrder, Announcement, Message, Document, Meeting, Customer,
    ProductCategory, Product, Report, SalesOrder, Workflow,
    # New Financial Models
    Currency, ExchangeRate, AccountType, ChartOfAccounts, FiscalYear, JournalEntryBatch, JournalEntry,
    Vendor, VendorInvoice, CustomerInvoice, Payment,

    # Enhanced Asset Management Models
    AssetCategory, Asset, AssetDepreciation, AssetMaintenance, AssetTransfer, AssetAudit,

    # Advanced Reporting & Analytics Models
    KPIMetric, KPIMetricValue, ReportTemplate, ReportExecution, Dashboard, AnalyticsQuery,

    # Integration & API Management Models
    APIKey, ExternalService, WebhookEndpoint, WebhookEvent, IntegrationLog,

    # Security & Compliance Models
    UserSecurityProfile, AuditTrail, SecurityIncident,
    ComplianceFramework, ComplianceControl, DataClassification, SecurityAlert
)
from .serializers import (
    DepartmentSerializer, EmployeeSerializer, ActivitySerializer, DashboardStatsSerializer,
    UserSerializer, RoleSerializer, UserProfileSerializer, LeaveTypeSerializer,
    LeaveRequestSerializer, EmployeeLeaveSerializer, AttendanceSerializer, ProjectSerializer,
    TaskSerializer, BudgetSerializer, ExpenseSerializer, AssetCategorySerializer, AssetSerializer,
    SupplierSerializer, PurchaseOrderSerializer, AnnouncementSerializer,
    MessageSerializer, DocumentSerializer, MeetingSerializer, PersonalCalendarSerializer,
    CustomerSerializer, ProductCategorySerializer, ProductSerializer, ReportSerializer,
    SalesOrderSerializer, WorkflowSerializer,
    # New Financial Serializers
    AccountTypeSerializer, ChartOfAccountsSerializer, FiscalYearSerializer,
    JournalEntryBatchSerializer, JournalEntrySerializer, VendorSerializer,
    VendorInvoiceSerializer, CustomerInvoiceSerializer, PaymentSerializer,
    CurrencySerializer, ExchangeRateSerializer,

    # Enhanced Asset Management Serializers
    AssetCategorySerializer, AssetSerializer, AssetDepreciationSerializer,
    AssetMaintenanceSerializer, AssetTransferSerializer, AssetAuditSerializer,

    # Advanced Reporting & Analytics Serializers
    KPIMetricSerializer, KPIMetricValueSerializer, ReportTemplateSerializer,
    ReportExecutionSerializer, DashboardSerializer, AnalyticsQuerySerializer,

    # Integration & API Management Serializers
    APIKeySerializer, ExternalServiceSerializer, WebhookEndpointSerializer,
    WebhookEventSerializer, IntegrationLogSerializer,

    # Security & Compliance Serializers
    UserSecurityProfileSerializer, AuditTrailSerializer,
    SecurityIncidentSerializer, ComplianceFrameworkSerializer, ComplianceControlSerializer,
    DataClassificationSerializer, SecurityAlertSerializer
)

class DepartmentViewSet(viewsets.ModelViewSet):
    # CRITICAL FIX: Add select_related to prevent N+1 queries
    # Note: 'employees' should be 'employee_set' for reverse relationship
    queryset = Department.objects.select_related('manager__user').prefetch_related('employee_set').all()
    serializer_class = DepartmentSerializer
    permission_classes = [IsAuthenticated]

class EmployeeViewSet(viewsets.ModelViewSet):
    queryset = Employee.objects.select_related('user', 'department').all()
    serializer_class = EmployeeSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        from django.db.models import Q
        queryset = super().get_queryset()

        # Department filter
        department = self.request.query_params.get('department', None)
        if department is not None:
            # Try to filter by department ID first, then by name
            try:
                # If it's a number, filter by ID
                dept_id = int(department)
                queryset = queryset.filter(department_id=dept_id)
            except ValueError:
                # If it's not a number, filter by department name
                queryset = queryset.filter(department__name__icontains=department)

        # Search functionality
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(user__first_name__icontains=search) |
                Q(user__last_name__icontains=search) |
                Q(first_name_ar__icontains=search) |
                Q(last_name_ar__icontains=search) |
                Q(user__email__icontains=search) |
                Q(employee_id__icontains=search) |
                Q(position__icontains=search) |
                Q(position_ar__icontains=search) |
                Q(department__name__icontains=search) |
                Q(department__name_ar__icontains=search) |
                Q(phone__icontains=search)
            )

        # Employment status filter
        employment_status = self.request.query_params.get('employment_status', None)
        if employment_status is not None:
            queryset = queryset.filter(employment_status=employment_status)

        # Active status filter
        is_active = self.request.query_params.get('is_active', None)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('user__first_name', 'user__last_name')

    @action(detail=False, methods=['get'], url_path='dropdown-list')
    def dropdown_list(self, request):
        """Get simplified employee list for dropdowns"""
        try:
            employees = Employee.objects.select_related('user').filter(is_active=True)

            # Apply department filter if provided
            department = request.query_params.get('department')
            if department:
                employees = employees.filter(department_id=department)

            # Create simplified data for dropdowns
            employee_list = []
            for emp in employees:
                employee_list.append({
                    'id': emp.id,
                    'employee_id': emp.employee_id,
                    'name': emp.user.get_full_name(),
                    'name_ar': f"{emp.first_name_ar} {emp.last_name_ar}" if emp.first_name_ar and emp.last_name_ar else emp.user.get_full_name(),
                    'department': emp.department.name if emp.department else None,
                    'position': emp.position,
                    'email': emp.user.email
                })

            return Response(employee_list, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {'error': f'Failed to fetch employee list: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class ActivityViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Activity.objects.select_related('user').all()
    serializer_class = ActivitySerializer
    permission_classes = [IsAuthenticated]

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def employee_export(request):
    """Export employees data in various formats"""
    import csv
    from django.http import HttpResponse

    # Get format parameter (default to csv)
    export_format = request.GET.get('format', 'csv').lower()

    # Get employees queryset
    queryset = Employee.objects.select_related('user', 'department').all()

    # Apply department filter if provided
    department = request.GET.get('department')
    if department:
        queryset = queryset.filter(department=department)

    # Prepare data
    headers = [
        'Employee ID', 'First Name', 'Last Name', 'Email', 'Position',
        'Position (Arabic)', 'Department', 'Department (Arabic)',
        'Phone', 'Gender', 'Hire Date', 'Salary', 'Status'
    ]

    data = []
    for employee in queryset:
        data.append([
            employee.employee_id,
            employee.user.first_name,
            employee.user.last_name,
            employee.user.email,
            employee.position,
            employee.position_ar,
            employee.department.name if employee.department else '',
            employee.department.name_ar if employee.department else '',
            employee.phone,
            employee.get_gender_display(),
            employee.hire_date,
            employee.salary,
            'Active' if employee.is_active else 'Inactive'
        ])

    if export_format == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="employees.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)
        writer.writerows(data)

        return response

    elif export_format == 'excel':
        try:
            import openpyxl
            from openpyxl import Workbook
            from io import BytesIO

            wb = Workbook()
            ws = wb.active
            ws.title = "Employees"

            # Add headers
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # Add data
            for row, row_data in enumerate(data, 2):
                for col, value in enumerate(row_data, 1):
                    ws.cell(row=row, column=col, value=value)

            # Save to BytesIO
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="employees.xlsx"'
            return response

        except ImportError:
            # Fallback to CSV if openpyxl is not available
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="employees.csv"'

            writer = csv.writer(response)
            writer.writerow(headers)
            writer.writerows(data)

            return response

    else:
        # Default to CSV for unsupported formats
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="employees.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)
        writer.writerows(data)

        return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def department_export(request):
    """Export departments data in various formats"""
    import csv
    from django.http import HttpResponse

    # Get format parameter (default to csv)
    export_format = request.GET.get('format', 'csv').lower()

    # CRITICAL FIX: Get departments queryset with optimized queries
    # Note: 'employees' should be 'employee_set' for reverse relationship
    queryset = Department.objects.select_related('manager__user').prefetch_related('employee_set').all()

    # Apply filters if provided
    is_active = request.GET.get('is_active')
    if is_active:
        queryset = queryset.filter(is_active=is_active.lower() == 'true')

    # Prepare data
    headers = [
        'Department ID', 'Name', 'Name (Arabic)', 'Description', 'Description (Arabic)',
        'Manager', 'Manager (Arabic)', 'Location', 'Phone', 'Email', 'Budget',
        'Employee Count', 'Status', 'Created Date'
    ]

    data = []
    for department in queryset:
        # Get manager information
        manager_name = ''
        manager_name_ar = ''
        if department.manager:
            manager_name = department.manager.user.get_full_name()
            manager_name_ar = f"{department.manager.first_name_ar or ''} {department.manager.last_name_ar or ''}".strip()

        data.append([
            department.id,
            department.name,
            department.name_ar or '',
            department.description or '',
            department.description_ar or '',
            manager_name,
            manager_name_ar,
            department.location or '',
            department.phone or '',
            department.email or '',
            department.budget_amount or 0,
            department.employees.count() if hasattr(department, 'employees') else 0,
            'Active' if department.is_active else 'Inactive',
            department.created_at.strftime('%Y-%m-%d') if department.created_at else ''
        ])

    if export_format == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="departments.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)
        writer.writerows(data)

        return response

    elif export_format == 'excel':
        try:
            import openpyxl
            from openpyxl import Workbook
            from io import BytesIO

            wb = Workbook()
            ws = wb.active
            ws.title = "Departments"

            # Add headers
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # Add data
            for row, row_data in enumerate(data, 2):
                for col, value in enumerate(row_data, 1):
                    ws.cell(row=row, column=col, value=value)

            # Save to BytesIO
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="departments.xlsx"'
            return response

        except ImportError:
            # Fallback to CSV if openpyxl is not available
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="departments.csv"'

            writer = csv.writer(response)
            writer.writerow(headers)
            writer.writerows(data)

            return response

    else:
        # Default to CSV for unsupported formats
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="departments.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)
        writer.writerows(data)

        return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def attendance_export(request):
    """Export attendance data in various formats"""
    import csv
    from django.http import HttpResponse

    # Get format parameter (default to csv)
    export_format = request.GET.get('format', 'csv').lower()

    # Get attendance queryset
    queryset = Attendance.objects.select_related('employee__user').all()

    # Apply filters if provided
    employee = request.GET.get('employee')
    if employee:
        queryset = queryset.filter(employee=employee)

    date_from = request.GET.get('date_from')
    if date_from:
        queryset = queryset.filter(date__gte=date_from)

    date_to = request.GET.get('date_to')
    if date_to:
        queryset = queryset.filter(date__lte=date_to)

    # Prepare data
    headers = [
        'Employee ID', 'Employee Name', 'Date', 'Check In', 'Check Out',
        'Break Start', 'Break End', 'Total Hours', 'Overtime Hours',
        'Present', 'Late', 'Notes'
    ]

    data = []
    for attendance in queryset:
        data.append([
            attendance.employee.employee_id,
            attendance.employee.user.get_full_name(),
            attendance.date.strftime('%Y-%m-%d'),
            attendance.check_in.strftime('%H:%M:%S') if attendance.check_in else '',
            attendance.check_out.strftime('%H:%M:%S') if attendance.check_out else '',
            attendance.break_start.strftime('%H:%M:%S') if attendance.break_start else '',
            attendance.break_end.strftime('%H:%M:%S') if attendance.break_end else '',
            str(attendance.total_hours) if attendance.total_hours else '',
            str(attendance.overtime_hours) if attendance.overtime_hours else '0',
            'Yes' if attendance.is_present else 'No',
            'Yes' if attendance.is_late else 'No',
            attendance.notes or ''
        ])

    if export_format == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="attendance.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)
        writer.writerows(data)

        return response

    elif export_format == 'excel':
        try:
            import openpyxl
            from openpyxl import Workbook
            from io import BytesIO

            wb = Workbook()
            ws = wb.active
            ws.title = "Attendance"

            # Add headers
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # Add data
            for row, row_data in enumerate(data, 2):
                for col, value in enumerate(row_data, 1):
                    ws.cell(row=row, column=col, value=value)

            # Save to BytesIO
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="attendance.xlsx"'
            return response

        except ImportError:
            # Fallback to CSV if openpyxl is not available
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="attendance.csv"'

            writer = csv.writer(response)
            writer.writerow(headers)
            writer.writerows(data)

            return response

    else:
        # Default to CSV for unsupported formats
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="attendance.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)
        writer.writerows(data)

        return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def test_export(request):
    """Test export function - now returns attendance CSV"""
    import csv
    from django.http import HttpResponse

    # Get format parameter (default to csv)
    export_format = request.GET.get('format', 'csv').lower()

    # Get attendance queryset
    queryset = Attendance.objects.select_related('employee__user').all()[:10]  # Limit to 10 for testing

    # Prepare data
    headers = [
        'Employee ID', 'Employee Name', 'Date', 'Check In', 'Check Out',
        'Total Hours', 'Present', 'Late'
    ]

    if export_format == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="attendance.csv"'

        writer = csv.writer(response)
        writer.writerow(headers)

        for attendance in queryset:
            writer.writerow([
                attendance.employee.employee_id,
                attendance.employee.user.get_full_name(),
                attendance.date.strftime('%Y-%m-%d'),
                attendance.check_in.strftime('%H:%M:%S') if attendance.check_in else '',
                attendance.check_out.strftime('%H:%M:%S') if attendance.check_out else '',
                str(attendance.total_hours) if attendance.total_hours else '',
                'Yes' if attendance.is_present else 'No',
                'Yes' if attendance.is_late else 'No',
            ])

        return response
    else:
        response = HttpResponse("Attendance export working! Format: " + export_format, content_type='text/plain')
        return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def simple_attendance_export(request):
    """Simple attendance export function"""
    from django.http import HttpResponse
    response = HttpResponse("Simple attendance export working!", content_type='text/plain')
    return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@cache_page(60 * 5)  # Cache for 5 minutes
def dashboard_stats(request):
    """
    Get dashboard statistics matching frontend interface
    """
    from django.db.models import Sum, Count
    import psutil

    try:
        total_employees = Employee.objects.filter(is_active=True).count()
        total_departments = Department.objects.count()
        active_projects = Project.objects.filter(status='IN_PROGRESS').count()
        pending_tasks = Task.objects.filter(status='TODO').count()
        pending_leave_requests = LeaveRequest.objects.filter(status='PENDING').count()

        # Calculate monthly expenses (current month)
        from datetime import datetime, timedelta
        current_month = datetime.now().replace(day=1)
        monthly_expenses = Expense.objects.filter(
            expense_date__gte=current_month,
            status='APPROVED'
        ).aggregate(total=Sum('amount'))['total'] or 0

        # System health metrics
        try:
            cpu_usage = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            system_health = {
                'cpu_usage': cpu_usage,
                'memory_usage': memory.percent,
                'disk_usage': (disk.used / disk.total) * 100
            }
        except:
            # Fallback if psutil is not available - return empty states instead of fake data
            system_health = {
                'cpu_usage': 0.0,
                'memory_usage': 0.0,
                'disk_usage': 0.0
            }

        data = {
            'total_employees': total_employees,
            'active_employees': total_employees,  # Add active_employees for compatibility
            'total_departments': total_departments,
            'active_projects': active_projects,
            'pending_tasks': pending_tasks,
            'pending_leave_requests': pending_leave_requests,
            'monthly_expenses': float(monthly_expenses),
            'system_health': system_health
        }

        return Response(data)

    except Exception as e:
        # Log the error for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Dashboard stats error: {str(e)}")

        # Return fallback data instead of error
        total_emp_count = Employee.objects.count()
        fallback_data = {
            'total_employees': total_emp_count,
            'active_employees': total_emp_count,  # Add active_employees for compatibility
            'total_departments': Department.objects.count(),
            'active_projects': 0,
            'pending_tasks': 0,
            'pending_leave_requests': 0,
            'monthly_expenses': 0.0,
            'system_health': {
                'cpu_usage': 0.0,
                'memory_usage': 0.0,
                'disk_usage': 0.0
            }
        }

        return Response(fallback_data)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def superadmin_system_stats(request):
    """
    Get real system administration statistics for SUPERADMIN
    """
    import psutil
    import platform
    import os
    from django.db import connection
    from django.contrib.auth.models import User
    from django.contrib.sessions.models import Session
    from datetime import datetime, timedelta

    # Check if user has SUPERADMIN role
    try:
        user_profile = UserProfile.objects.get(user=request.user)
        if user_profile.role.name != 'SUPERADMIN':
            return Response({
                'error': 'Access denied. SUPERADMIN role required.'
            }, status=status.HTTP_403_FORBIDDEN)
    except UserProfile.DoesNotExist:
        return Response({
            'error': 'User profile not found'
        }, status=status.HTTP_404_NOT_FOUND)

    try:
        # Real System Metrics
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time

        # Network statistics
        network = psutil.net_io_counters()

        # Database statistics
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM django_session WHERE expire_date > %s", [datetime.now()])
            active_sessions = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM auth_user WHERE is_active = true")
            total_users = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM auth_user WHERE last_login > %s", [datetime.now() - timedelta(hours=24)])
            active_users_24h = cursor.fetchone()[0]

            # Get database size (PostgreSQL specific, fallback for other DBs)
            try:
                cursor.execute("SELECT pg_size_pretty(pg_database_size(current_database()))")
                db_size = cursor.fetchone()[0]
            except:
                db_size = "N/A"

        # Security metrics
        failed_logins_24h = 0  # This would need to be tracked in a separate model
        blocked_ips = 0  # This would need to be tracked in a separate model

        # System information
        system_info = {
            'platform': platform.platform(),
            'python_version': platform.python_version(),
            'architecture': platform.architecture()[0],
            'processor': platform.processor() or 'Unknown',
            'hostname': platform.node()
        }

        # Format uptime
        uptime_str = f"{uptime.days} days, {uptime.seconds // 3600} hours"

        data = {
            'system_health': {
                'status': 'healthy' if cpu_usage < 80 and memory.percent < 85 else 'warning' if cpu_usage < 95 and memory.percent < 95 else 'critical',
                'uptime': uptime_str,
                'boot_time': boot_time.isoformat()
            },
            'server_metrics': {
                'cpu_usage': round(cpu_usage, 1),
                'memory_usage': round(memory.percent, 1),
                'memory_total': round(memory.total / (1024**3), 2),  # GB
                'memory_available': round(memory.available / (1024**3), 2),  # GB
                'disk_usage': round((disk.used / disk.total) * 100, 1),
                'disk_total': round(disk.total / (1024**3), 2),  # GB
                'disk_free': round(disk.free / (1024**3), 2),  # GB
                'network_sent': round(network.bytes_sent / (1024**2), 2),  # MB
                'network_recv': round(network.bytes_recv / (1024**2), 2)   # MB
            },
            'database_stats': {
                'size': db_size,
                'active_connections': active_sessions,
                'total_tables': len(connection.introspection.table_names()),
                'status': 'online'
            },
            'user_stats': {
                'total_system_users': total_users,
                'active_sessions': active_sessions,
                'active_users_24h': active_users_24h,
                'superadmin_users': UserProfile.objects.filter(role__name='SUPERADMIN').count(),
                'admin_users': UserProfile.objects.filter(role__name='ADMIN').count()
            },
            'security_stats': {
                'security_score': calculate_security_score(),
                'active_threats': get_active_threats_count(),
                'blocked_attacks': get_blocked_attacks_count(),
                'failed_logins_24h': failed_logins_24h,
                'blocked_ips': blocked_ips
            },
            'system_info': system_info,
            'last_updated': datetime.now().isoformat()
        }

        return Response(data)

    except Exception as e:
        return Response({
            'error': 'Failed to fetch system stats',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """
    Get current user profile
    """
    try:
        employee = Employee.objects.select_related('user', 'department').get(user=request.user)
        serializer = EmployeeSerializer(employee)
        return Response(serializer.data)
    except Employee.DoesNotExist:
        user_serializer = UserSerializer(request.user)
        return Response(user_serializer.data)

# HR Management ViewSets
class RoleViewSet(viewsets.ModelViewSet):
    queryset = Role.objects.all().order_by('name')
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated]

class UserProfileViewSet(viewsets.ModelViewSet):
    queryset = UserProfile.objects.select_related('user', 'role').all().order_by('user__first_name', 'user__last_name')
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated]

class LeaveTypeViewSet(viewsets.ModelViewSet):
    queryset = LeaveType.objects.filter(is_active=True)
    serializer_class = LeaveTypeSerializer
    permission_classes = [IsAuthenticated]

class LeaveRequestViewSet(viewsets.ModelViewSet):
    queryset = LeaveRequest.objects.select_related('employee__user', 'leave_type', 'approved_by__user').all()
    serializer_class = LeaveRequestSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'], url_path='my-requests', permission_classes=[IsAuthenticated])
    def my_requests(self, request):
        """Get current user's leave requests"""
        try:
            employee = Employee.objects.get(user=request.user)
            queryset = self.queryset.filter(employee=employee)
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except Employee.DoesNotExist:
            return Response([], status=status.HTTP_200_OK)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in my_requests for user {request.user.username}: {str(e)}")
            return Response([], status=status.HTTP_200_OK)

class AttendanceViewSet(viewsets.ModelViewSet):
    queryset = Attendance.objects.select_related('employee__user').all()
    serializer_class = AttendanceSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'], url_path='my-attendance', permission_classes=[IsAuthenticated])
    def my_attendance(self, request):
        """Get current user's attendance records"""
        try:
            employee = Employee.objects.get(user=request.user)
            queryset = self.queryset.filter(employee=employee)

            # Filter by date range if provided
            date_from = request.query_params.get('date_from')
            date_to = request.query_params.get('date_to')

            if date_from:
                queryset = queryset.filter(date__gte=date_from)
            if date_to:
                queryset = queryset.filter(date__lte=date_to)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except Employee.DoesNotExist:
            return Response([], status=status.HTTP_200_OK)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in my_attendance for user {request.user.username}: {str(e)}")
            return Response([], status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'], url_path='trends', permission_classes=[IsAuthenticated])
    def trends(self, request):
        """Get attendance trends for dashboard charts"""
        try:
            from datetime import datetime, timedelta
            from django.db.models import Count, Avg

            # Get date range (default to last 30 days)
            days = int(request.query_params.get('days', 30))
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=days)

            # Calculate daily attendance trends
            trends = []
            current_date = start_date

            while current_date <= end_date:
                daily_attendance = Attendance.objects.filter(date=current_date)

                total_employees = Employee.objects.filter(is_active=True).count()
                present_count = daily_attendance.filter(status='PRESENT').count()
                absent_count = daily_attendance.filter(status='ABSENT').count()
                late_count = daily_attendance.filter(status='LATE').count()

                attendance_rate = (present_count / total_employees * 100) if total_employees > 0 else 0

                trends.append({
                    'date': current_date.strftime('%Y-%m-%d'),
                    'present': present_count,
                    'absent': absent_count,
                    'late': late_count,
                    'total_employees': total_employees,
                    'attendance_rate': round(attendance_rate, 2)
                })

                current_date += timedelta(days=1)

            return Response({
                'trends': trends,
                'summary': {
                    'total_days': len(trends),
                    'average_attendance_rate': round(sum(t['attendance_rate'] for t in trends) / len(trends), 2) if trends else 0
                }
            })

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in attendance trends: {str(e)}")
            return Response({
                'trends': [],
                'summary': {'total_days': 0, 'average_attendance_rate': 0}
            }, status=status.HTTP_200_OK)

# Project Management ViewSets
class ProjectViewSet(viewsets.ModelViewSet):
    queryset = Project.objects.select_related('project_manager__user', 'department').prefetch_related('team_members', 'tasks').all()
    serializer_class = ProjectSerializer
    permission_classes = [IsAuthenticated, EmployeePermission, EmployeeDataPermission]

    def get_queryset(self):
        """Filter projects based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        try:
            # FIXED: Use employee profile instead of userprofile for project filtering
            employee_profile = user.employee
            user_profile = user.userprofile
            user_role = user_profile.role

            # Super admin and admin see all projects
            if user_role and user_role.name in ['SUPERADMIN', 'ADMIN']:
                return queryset

            # Employees only see projects they're assigned to
            if user_role and user_role.name == 'EMPLOYEE':
                return queryset.filter(team_members=employee_profile)

            # Other roles see projects in their department
            if employee_profile.department:
                return queryset.filter(department=employee_profile.department)

            return queryset
        except:
            return queryset.none()

    @action(detail=True, methods=['patch'], url_path='update-progress', permission_classes=[IsAuthenticated])
    def update_progress(self, request, pk=None):
        """Update project progress - employees can update progress for projects they're assigned to"""
        try:
            project = self.get_object()
            user = request.user

            # Check if user is assigned to this project
            try:
                employee_profile = user.employee
                if not project.team_members.filter(id=employee_profile.id).exists():
                    return Response(
                        {'error': 'You are not assigned to this project'},
                        status=status.HTTP_403_FORBIDDEN
                    )
            except:
                return Response(
                    {'error': 'Employee profile not found'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Get progress data from request
            progress_percentage = request.data.get('progress_percentage')
            progress_note = request.data.get('progress_note', '')

            # Validate progress percentage
            if progress_percentage is None:
                return Response(
                    {'error': 'progress_percentage is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                progress_percentage = float(progress_percentage)
                if progress_percentage < 0 or progress_percentage > 100:
                    return Response(
                        {'error': 'progress_percentage must be between 0 and 100'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            except (ValueError, TypeError):
                return Response(
                    {'error': 'progress_percentage must be a valid number'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Update project progress
            project.progress_percentage = progress_percentage
            project.save()

            # TODO: Create a ProjectProgressUpdate model to track progress history
            # For now, we'll just update the project directly

            # Return updated project data
            serializer = self.get_serializer(project)
            return Response({
                'message': 'Progress updated successfully',
                'project': serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {'error': f'Failed to update progress: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class TaskViewSet(viewsets.ModelViewSet):
    queryset = Task.objects.select_related('project', 'assigned_to__user', 'created_by__user').all()
    serializer_class = TaskSerializer
    permission_classes = [IsAuthenticated, EmployeePermission, EmployeeDataPermission]

    def get_queryset(self):
        """Filter tasks based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        try:
            user_profile = user.userprofile
            user_role = user_profile.role

            # Super admin and admin see all tasks
            if user_role and user_role.name in ['SUPERADMIN', 'ADMIN']:
                return queryset

            # Employees only see tasks assigned to them
            if user_role and user_role.name == 'EMPLOYEE':
                return queryset.filter(assigned_to=user_profile)

            # HR managers see all tasks
            if user_role and user_role.name == 'HR_MANAGER':
                return queryset

            # Other roles see tasks in their department
            if user_profile.department:
                return queryset.filter(
                    Q(assigned_to__department=user_profile.department) |
                    Q(project__department=user_profile.department)
                )

            return queryset
        except:
            return queryset.none()

    @action(detail=False, methods=['get'], url_path='my-tasks', permission_classes=[IsAuthenticated])
    def my_tasks(self, request):
        """Get current user's assigned tasks"""
        try:
            # Try to get Employee record first
            employee = Employee.objects.get(user=request.user)
            queryset = self.queryset.filter(assigned_to=employee)
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except Employee.DoesNotExist:
            # If no Employee record, return empty list
            return Response([], status=status.HTTP_200_OK)
        except Exception as e:
            # Log any other errors and return empty list
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in my_tasks for user {request.user.username}: {str(e)}")
            return Response([], status=status.HTTP_200_OK)

# Financial Management ViewSets
class BudgetViewSet(viewsets.ModelViewSet):
    queryset = Budget.objects.select_related('department', 'project', 'created_by__user').all()
    serializer_class = BudgetSerializer
    permission_classes = [IsAuthenticated]

class ExpenseViewSet(viewsets.ModelViewSet):
    queryset = Expense.objects.select_related('employee__user', 'budget', 'project', 'approved_by__user').all()
    serializer_class = ExpenseSerializer
    permission_classes = [IsAuthenticated]

# Asset Management ViewSets
class AssetCategoryViewSet(viewsets.ModelViewSet):
    queryset = AssetCategory.objects.all()
    serializer_class = AssetCategorySerializer
    permission_classes = [IsAuthenticated]

class AssetViewSet(viewsets.ModelViewSet):
    queryset = Asset.objects.select_related('category', 'assigned_to__user', 'assigned_to__department').all()
    serializer_class = AssetSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Filter assets based on user role and permissions
        """
        user = self.request.user

        # Get user's role
        try:
            user_role = user.userprofile.role.name if user.userprofile.role else None
        except:
            user_role = None

        # Get user's employee record
        try:
            employee = user.employee
        except:
            employee = None

        base_queryset = Asset.objects.select_related('category', 'assigned_to__user', 'assigned_to__department').all()

        # Role-based filtering
        if user_role in ['SUPERADMIN', 'ADMIN']:
            # Full access to all assets
            return base_queryset

        elif user_role == 'HR_MANAGER':
            # HR can see all assets for management purposes
            return base_queryset

        elif user_role == 'FINANCE_MANAGER':
            # Finance can see all assets for budgeting/cost tracking
            return base_queryset

        elif user_role == 'DEPARTMENT_MANAGER':
            # Department managers see assets in their department + assigned to them
            if employee and employee.department:
                return base_queryset.filter(
                    models.Q(assigned_to__department=employee.department) |
                    models.Q(assigned_to=employee)
                ).distinct()
            else:
                return base_queryset.filter(assigned_to=employee) if employee else base_queryset.none()

        elif user_role == 'PROJECT_MANAGER':
            # Project managers see assets assigned to them + their department
            if employee and employee.department:
                return base_queryset.filter(
                    models.Q(assigned_to__department=employee.department) |
                    models.Q(assigned_to=employee)
                ).distinct()
            else:
                return base_queryset.filter(assigned_to=employee) if employee else base_queryset.none()

        elif user_role in ['EMPLOYEE', 'INTERN']:
            # Regular employees see only assets assigned to them + shared department assets
            if employee:
                return base_queryset.filter(
                    models.Q(assigned_to=employee) |
                    models.Q(assigned_to__isnull=True, location__icontains=employee.department.name if employee.department else '')
                ).distinct()
            else:
                return base_queryset.none()

        else:
            # No role or unknown role - no access
            return base_queryset.none()

    def perform_create(self, serializer):
        """
        Only allow certain roles to create assets
        """
        user = self.request.user
        try:
            user_role = user.userprofile.role.name if user.userprofile.role else None
        except:
            user_role = None

        if user_role not in ['SUPERADMIN', 'ADMIN', 'HR_MANAGER']:
            from rest_framework.exceptions import PermissionDenied
            raise PermissionDenied("You don't have permission to create assets.")

        serializer.save()

    def perform_update(self, serializer):
        """
        Role-based update permissions
        """
        user = self.request.user
        try:
            user_role = user.userprofile.role.name if user.userprofile.role else None
        except:
            user_role = None

        if user_role not in ['SUPERADMIN', 'ADMIN', 'HR_MANAGER']:
            from rest_framework.exceptions import PermissionDenied
            raise PermissionDenied("You don't have permission to edit assets.")

        serializer.save()

    def perform_destroy(self, instance):
        """
        Only admins can delete assets
        """
        user = self.request.user
        try:
            user_role = user.userprofile.role.name if user.userprofile.role else None
        except:
            user_role = None

        if user_role not in ['SUPERADMIN', 'ADMIN']:
            from rest_framework.exceptions import PermissionDenied
            raise PermissionDenied("You don't have permission to delete assets.")

        instance.delete()

class SupplierViewSet(viewsets.ModelViewSet):
    queryset = Supplier.objects.filter(is_active=True)
    serializer_class = SupplierSerializer
    permission_classes = [IsAuthenticated]

class PurchaseOrderViewSet(viewsets.ModelViewSet):
    queryset = PurchaseOrder.objects.select_related('supplier', 'requested_by__user', 'approved_by__user', 'department', 'project').all()
    serializer_class = PurchaseOrderSerializer
    permission_classes = [IsAuthenticated]

# Communication & Collaboration ViewSets
class AnnouncementViewSet(viewsets.ModelViewSet):
    queryset = Announcement.objects.select_related('author__user').prefetch_related('target_departments', 'target_employees').all()
    serializer_class = AnnouncementSerializer
    permission_classes = [IsAuthenticated, EmployeePermission]

    def get_queryset(self):
        """Filter announcements based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        try:
            # Try to get user profile and role
            employee = Employee.objects.select_related('user').get(user=user)
            user_profile = getattr(user, 'userprofile', None)
            user_role = getattr(user_profile, 'role', None) if user_profile else None

            # Super admin and admin see all announcements
            if user_role and user_role.name in ['SUPERADMIN', 'ADMIN']:
                return queryset

            # Employees only see published announcements targeted to them
            if user_role and user_role.name == 'EMPLOYEE':
                return queryset.filter(
                    is_published=True
                ).filter(
                    Q(target_departments=employee.department) |
                    Q(target_employees=employee) |
                    Q(target_departments__isnull=True, target_employees__isnull=True)  # General announcements
                )

            # Other roles see all published announcements
            return queryset.filter(is_published=True)
        except Employee.DoesNotExist:
            # If user has no employee record, show only published general announcements
            return queryset.filter(
                is_published=True,
                target_departments__isnull=True,
                target_employees__isnull=True
            )
        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in AnnouncementViewSet.get_queryset: {str(e)}")
            # Return only published announcements as fallback
            return queryset.filter(is_published=True)

class MessageViewSet(viewsets.ModelViewSet):
    queryset = Message.objects.select_related('sender__user', 'recipient__user').all()
    serializer_class = MessageSerializer
    permission_classes = [IsAuthenticated]

class DocumentViewSet(viewsets.ModelViewSet):
    queryset = Document.objects.select_related('uploaded_by__user', 'department').prefetch_related('access_permissions').all()
    serializer_class = DocumentSerializer
    permission_classes = [IsAuthenticated, EmployeePermission]

    def get_queryset(self):
        """Filter documents based on user role and access level"""
        queryset = super().get_queryset()
        user = self.request.user

        try:
            user_profile = user.userprofile
            user_role = user_profile.role

            # Super admin and admin see all documents
            if user_role and user_role.name in ['SUPERADMIN', 'ADMIN']:
                return queryset

            # Employees only see public and internal documents
            if user_role and user_role.name == 'EMPLOYEE':
                return queryset.filter(access_level__in=['public', 'internal'])

            # HR managers see HR-related documents
            if user_role and user_role.name == 'HR_MANAGER':
                return queryset.filter(
                    Q(access_level__in=['public', 'internal', 'restricted']) |
                    Q(department__name='Human Resources')
                )

            # Other roles see documents based on department and access level
            return queryset.filter(
                Q(access_level__in=['public', 'internal']) |
                Q(department=user_profile.department)
            )
        except:
            return queryset.filter(access_level='public')

class MeetingViewSet(viewsets.ModelViewSet):
    queryset = Meeting.objects.select_related('organizer__user').prefetch_related('attendees').all()
    serializer_class = MeetingSerializer
    permission_classes = [IsAuthenticated]

class PersonalCalendarViewSet(viewsets.ModelViewSet):
    """Personal Calendar ViewSet - provides calendar events for the current user"""
    serializer_class = PersonalCalendarSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Get meetings where user is organizer or attendee"""
        try:
            employee = Employee.objects.get(user=self.request.user)
            return Meeting.objects.select_related('organizer__user').prefetch_related('attendees').filter(
                models.Q(organizer=employee) | models.Q(attendees=employee)
            ).distinct()
        except Employee.DoesNotExist:
            return Meeting.objects.none()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in PersonalCalendarViewSet.get_queryset for user {self.request.user.username}: {str(e)}")
            return Meeting.objects.none()

class EmployeeLeaveViewSet(viewsets.ModelViewSet):
    """Employee Leave ViewSet - provides leave requests with frontend-compatible data structure"""
    serializer_class = EmployeeLeaveSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['leave_type__name', 'leave_type__name_ar', 'reason', 'reason_ar', 'status']
    filterset_fields = ['status', 'leave_type']
    ordering_fields = ['created_at', 'start_date', 'end_date']
    ordering = ['-created_at']

    def create(self, request, *args, **kwargs):
        """Override create to add debugging"""
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"EmployeeLeaveViewSet.create - Request data: {request.data}")
        logger.info(f"EmployeeLeaveViewSet.create - User: {request.user.username}")

        try:
            return super().create(request, *args, **kwargs)
        except Exception as e:
            logger.error(f"EmployeeLeaveViewSet.create - Error: {e}")
            raise

    def destroy(self, request, *args, **kwargs):
        """Override destroy to return proper JSON response"""
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return Response(
                {"success": True, "message": "Leave request deleted successfully"},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"EmployeeLeaveViewSet.destroy - Error: {e}")
            return Response(
                {"success": False, "message": "Failed to delete leave request"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_queryset(self):
        """Get leave requests for the current user"""
        try:
            employee = Employee.objects.get(user=self.request.user)
            return LeaveRequest.objects.select_related('employee__user', 'leave_type', 'approved_by__user').filter(
                employee=employee
            ).order_by('-created_at')
        except Employee.DoesNotExist:
            return LeaveRequest.objects.none()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in EmployeeLeaveViewSet.get_queryset for user {self.request.user.username}: {str(e)}")
            return LeaveRequest.objects.none()

    @action(detail=False, methods=['get'], url_path='my-requests', permission_classes=[IsAuthenticated])
    def my_requests(self, request):
        """Get current user's leave requests (alias for compatibility)"""
        try:
            employee = Employee.objects.get(user=request.user)
            queryset = LeaveRequest.objects.select_related('employee__user', 'leave_type', 'approved_by__user').filter(
                employee=employee
            ).order_by('-created_at')
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except Employee.DoesNotExist:
            return Response([], status=status.HTTP_200_OK)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in EmployeeLeaveViewSet.my_requests for user {request.user.username}: {str(e)}")
            return Response([], status=status.HTTP_200_OK)

# Customer Management ViewSets
class CustomerViewSet(viewsets.ModelViewSet):
    # CRITICAL FIX: Remove invalid select_related field
    # Customer model doesn't have 'assigned_to' field
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by customer type
        customer_type = self.request.query_params.get('customer_type')
        if customer_type:
            queryset = queryset.filter(customer_type=customer_type)

        # Search by name or email
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(email__icontains=search) |
                Q(company_name__icontains=search)
            )

        return queryset.order_by('-created_at')

# Product Management ViewSets
class ProductCategoryViewSet(viewsets.ModelViewSet):
    queryset = ProductCategory.objects.all()
    serializer_class = ProductCategorySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('name')

class ProductViewSet(viewsets.ModelViewSet):
    queryset = Product.objects.select_related('category', 'supplier').all()
    serializer_class = ProductSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by category
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)

        # Search by name or SKU
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(sku__icontains=search) |
                Q(brand__icontains=search)
            )

        return queryset.order_by('-created_at')

# Report Management ViewSets
class ReportViewSet(viewsets.ModelViewSet):
    queryset = Report.objects.select_related('created_by').all()
    serializer_class = ReportSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by type
        type_filter = self.request.query_params.get('type')
        if type_filter:
            queryset = queryset.filter(type=type_filter)

        # Search by name or description
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search) |
                Q(name_ar__icontains=search) |
                Q(description_ar__icontains=search)
            )

        return queryset.order_by('-created_date')

    def perform_create(self, serializer):
        # Get the employee associated with the current user
        try:
            employee = Employee.objects.get(user=self.request.user)
            serializer.save(created_by=employee)
        except Employee.DoesNotExist:
            # If no employee found, create a basic one or handle appropriately
            serializer.save(created_by=None)

# Workflow Management ViewSets
class WorkflowViewSet(viewsets.ModelViewSet):
    queryset = Workflow.objects.select_related('created_by__user').all()
    serializer_class = WorkflowSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by category
        category = self.request.query_params.get('category', None)
        if category:
            queryset = queryset.filter(category=category)

        # Filter by status
        status = self.request.query_params.get('status', None)
        if status:
            queryset = queryset.filter(status=status)

        # Filter by priority
        priority = self.request.query_params.get('priority', None)
        if priority:
            queryset = queryset.filter(priority=priority)

        # Search functionality
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(name_ar__icontains=search) |
                Q(description__icontains=search) |
                Q(description_ar__icontains=search)
            )

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        # Set created_by to current user's employee profile if available
        try:
            employee = Employee.objects.get(user=self.request.user)
            serializer.save(created_by=employee)
        except Employee.DoesNotExist:
            serializer.save(created_by=None)

    @action(detail=True, methods=['post'])
    def execute(self, request, pk=None):
        """Execute a workflow manually"""
        workflow = self.get_object()

        # Update run count
        workflow.run_count += 1

        # Simulate execution (in real implementation, this would trigger actual workflow)
        import random
        success = random.choice([True, True, True, False])  # 75% success rate

        if success:
            workflow.success_count += 1

        workflow.last_run = timezone.now()
        workflow.save()

        return Response({
            'success': success,
            'message': 'Workflow executed successfully' if success else 'Workflow execution failed',
            'run_count': workflow.run_count,
            'success_rate': workflow.success_rate
        })

# Sales Management ViewSets
class SalesOrderViewSet(viewsets.ModelViewSet):
    queryset = SalesOrder.objects.select_related('customer').all()
    serializer_class = SalesOrderSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by priority
        priority_filter = self.request.query_params.get('priority')
        if priority_filter:
            queryset = queryset.filter(priority=priority_filter)

        # Search by order number or customer name
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(order_number__icontains=search) |
                Q(customer__first_name__icontains=search) |
                Q(customer__last_name__icontains=search) |
                Q(customer__company_name__icontains=search)
            )

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        # Auto-generate order number if not provided
        if not serializer.validated_data.get('order_number'):
            import uuid
            order_number = f"SO-{uuid.uuid4().hex[:8].upper()}"
            serializer.save(order_number=order_number)
        else:
            serializer.save()

# KPI Management ViewSets
from django.utils import timezone
from datetime import timedelta, date

class KPICategoryViewSet(viewsets.ModelViewSet):
    queryset = None  # Will be set in get_queryset
    serializer_class = None  # Will be set from serializers
    permission_classes = [IsAuthenticated]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from .models import KPICategory
        from .serializers import KPICategorySerializer
        self.queryset = KPICategory.objects.all()
        self.serializer_class = KPICategorySerializer

    def get_queryset(self):
        from .models import KPICategory
        queryset = KPICategory.objects.filter(is_active=True)
        return queryset.order_by('sort_order', 'name')

    @action(detail=True, methods=['get'])
    def kpis(self, request, pk=None):
        """Get all KPIs for a specific category"""
        category = self.get_object()
        kpis = category.kpis.filter(status='ACTIVE')

        # Filter by user role permissions
        user_profile = getattr(request.user, 'userprofile', None)
        if user_profile and user_profile.role:
            kpis = kpis.filter(
                Q(visible_to_roles=user_profile.role) |
                Q(visible_to_roles__isnull=True)
            )

        from .serializers import KPISerializer
        serializer = KPISerializer(kpis, many=True)
        return Response(serializer.data)


# REMOVED: Deprecated KPIViewSet - Use EnhancedKPIViewSet from enhanced_kpi_views.py instead

class RemovedKPIViewSet:
    """
    ⚠️ DEPRECATED: Legacy KPI ViewSet - DO NOT USE FOR NEW DEVELOPMENT

    This ViewSet is deprecated and will be removed in a future version.
    Use EnhancedKPIViewSet from enhanced_kpi_views.py instead.

    Reasons for deprecation:
    - Manual KPI entry disabled (enterprise automation only)
    - Lacks hierarchical access control
    - No real-time monitoring
    - Limited role-based filtering

    Migration path:
    - Replace with EnhancedKPIViewSet
    - Use /api/kpi/enhanced/ endpoints
    - Update frontend to use HierarchicalKPIDashboard
    """
    queryset = None  # Will be set in get_queryset
    serializer_class = None  # Will be set from serializers
    permission_classes = [IsAuthenticated]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from .models import KPI
        from .serializers import KPISerializer
        self.queryset = KPI.objects.all()
        self.serializer_class = KPISerializer

    def get_queryset(self):
        from .models import KPI
        queryset = KPI.objects.select_related('category', 'owner', 'created_by').prefetch_related('values')

        # Filter by user role permissions
        try:
            if hasattr(self.request, 'user') and self.request.user.is_authenticated:
                user_profile = getattr(self.request.user, 'userprofile', None)
                if user_profile and hasattr(user_profile, 'role') and user_profile.role:
                    queryset = queryset.filter(
                        Q(visible_to_roles=user_profile.role) |
                        Q(visible_to_roles__isnull=True)
                    )
        except (AttributeError, Exception) as e:
            # If there's any issue with user authentication or role access,
            # return all KPIs (for admin users) or handle gracefully
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"KPI role filtering failed: {str(e)}")
            pass

        # Advanced filtering - safely access query parameters
        query_params = getattr(self.request, 'query_params', self.request.GET)

        status_filter = query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        category = query_params.get('category')
        if category:
            queryset = queryset.filter(category__id=category)

        measurement_type = query_params.get('measurement_type')
        if measurement_type:
            queryset = queryset.filter(measurement_type=measurement_type)

        frequency = query_params.get('frequency')
        if frequency:
            queryset = queryset.filter(frequency=frequency)

        is_automated = query_params.get('is_automated')
        if is_automated is not None:
            queryset = queryset.filter(is_automated=is_automated.lower() == 'true')

        # Search functionality
        search = query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(name_ar__icontains=search) |
                Q(description__icontains=search) |
                Q(description_ar__icontains=search)
            )

        # Performance filtering
        performance = query_params.get('performance')
        if performance:
            # This would require complex calculation - simplified for now
            pass

        # Sorting
        sort_by = query_params.get('sort_by', 'name')
        sort_order = query_params.get('sort_order', 'asc')

        if sort_order == 'desc':
            sort_by = f'-{sort_by}'

        valid_sort_fields = ['name', 'name_ar', 'category__name', 'target_value', 'created_at', 'updated_at']
        if sort_by.lstrip('-') in valid_sort_fields:
            queryset = queryset.order_by(sort_by)
        else:
            queryset = queryset.order_by('category__sort_order', 'name')

        return queryset

    def list(self, request, *args, **kwargs):
        """Override list to add cache-busting headers and fresh data"""
        response = super().list(request, *args, **kwargs)

        # Add cache-busting headers
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
        response['X-KPI-Timestamp'] = timezone.now().isoformat()

        # Add metadata to response
        if hasattr(response, 'data') and 'results' in response.data:
            # Add summary statistics
            queryset = self.get_queryset()
            total_kpis = queryset.count()
            active_kpis = queryset.filter(status='ACTIVE').count()

            response.data['metadata'] = {
                'total_kpis': total_kpis,
                'active_kpis': active_kpis,
                'last_updated': timezone.now().isoformat(),
                'cache_timestamp': timezone.now().isoformat()
            }

        return response

    def perform_create(self, serializer):
        # Set the creator
        from django.shortcuts import get_object_or_404
        employee = get_object_or_404(Employee, user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=True, methods=['get'])
    def values(self, request, pk=None):
        """Get historical values for a KPI"""
        kpi = self.get_object()

        # Date range filtering
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        values = kpi.values.all()

        if start_date:
            values = values.filter(period_start__gte=start_date)
        if end_date:
            values = values.filter(period_end__lte=end_date)

        # Limit results
        limit = int(request.query_params.get('limit', 50))
        values = values[:limit]

        from .serializers import KPIValueSerializer
        serializer = KPIValueSerializer(values, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def trend(self, request, pk=None):
        """Get trend analysis for a KPI"""
        kpi = self.get_object()
        days = int(request.query_params.get('days', 30))

        from datetime import timedelta
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)

        values = kpi.values.filter(
            period_start__gte=start_date,
            period_end__lte=end_date
        ).order_by('period_start')

        data_points = []
        for value in values:
            data_points.append({
                'date': value.period_start.strftime('%Y-%m-%d'),
                'value': float(value.value),
                'period_start': value.period_start.isoformat(),
                'period_end': value.period_end.isoformat()
            })

        # Calculate trend direction
        if len(data_points) >= 2:
            first_value = data_points[0]['value']
            last_value = data_points[-1]['value']
            change_percentage = ((last_value - first_value) / first_value * 100) if first_value != 0 else 0

            if change_percentage > 5:
                trend_direction = 'up'
            elif change_percentage < -5:
                trend_direction = 'down'
            else:
                trend_direction = 'stable'
        else:
            trend_direction = 'stable'
            change_percentage = 0

        return Response({
            'kpi_id': str(kpi.id),
            'kpi_name': kpi.name,
            'data_points': data_points,
            'trend_direction': trend_direction,
            'change_percentage': round(change_percentage, 2)
        })

    @action(detail=True, methods=['post'])
    def calculate(self, request, pk=None):
        """Manually trigger KPI calculation using the automated engine"""
        kpi = self.get_object()

        if not kpi.calculation_method:
            return Response(
                {'error': 'KPI does not have a calculation method configured'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Use the new KPI calculation engine
            from .kpi_engine import KPICalculationEngine
            from django.utils import timezone

            engine = KPICalculationEngine()

            # Calculate for current month by default
            period_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            period_end = timezone.now()

            calculated_value = engine.calculate_kpi(kpi, period_start, period_end)

            if calculated_value is not None:
                # Create or update KPI value
                kpi_value, created = KPIValue.objects.update_or_create(
                    kpi=kpi,
                    period_start=period_start,
                    period_end=period_end,
                    defaults={
                        'value': calculated_value,
                        'recorded_by': kpi.created_by,
                        'is_estimated': False,
                        'confidence_level': 100.0,
                        'data_quality_score': 100.0,
                        'notes': f'Manually calculated via API',
                        'source_data': {
                            'calculation_method': kpi.calculation_method,
                            'engine': 'KPICalculationEngine',
                            'timestamp': timezone.now().isoformat(),
                            'trigger': 'manual_api'
                        }
                    }
                )

                # Update KPI current value
                kpi.current_value = calculated_value
                kpi.last_updated = timezone.now()
                kpi.save(update_fields=['current_value', 'last_updated'])

                return Response({
                    'success': True,
                    'message': 'KPI calculation completed successfully',
                    'current_value': float(calculated_value),
                    'last_updated': kpi.last_updated.isoformat(),
                    'period_start': period_start.isoformat(),
                    'period_end': period_end.isoformat(),
                    'created_new_value': created
                })
            else:
                return Response(
                    {'error': 'KPI calculation returned no value - check if operational data exists'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        except Exception as e:
            return Response(
                {'error': f'Failed to calculate KPI: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def bulk_calculate(self, request):
        """Calculate all automated KPIs using the new calculation engine"""
        try:
            from .kpi_engine import KPICalculationEngine

            engine = KPICalculationEngine()
            results = engine.calculate_all_kpis()

            return Response({
                'success': True,
                'message': f'KPI calculations completed: {results["calculated"]} calculated, {results["failed"]} failed',
                'results': {
                    'calculated_count': results['calculated'],
                    'failed_count': results['failed'],
                    'updated_kpis': results['updated_kpis'],
                    'failed_kpis': results['failed_kpis'],
                    'period_start': results['period_start'].isoformat(),
                    'period_end': results['period_end'].isoformat()
                },
                'cache_timestamp': timezone.now().isoformat()  # Add cache busting timestamp
            })
        except Exception as e:
            return Response(
                {'error': f'Failed to calculate KPIs: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def refresh_cache(self, request):
        """Force refresh KPI cache and return updated data"""
        try:
            # Clear any potential caching
            from django.core.cache import cache
            cache.clear()

            # Get fresh data
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            return Response({
                'success': True,
                'message': 'KPI cache refreshed successfully',
                'data': serializer.data,
                'cache_timestamp': timezone.now().isoformat(),
                'count': queryset.count()
            })
        except Exception as e:
            return Response(
                {'error': f'Failed to refresh cache: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def health_summary(self, request):
        """Get KPI health summary"""
        try:
            from .kpi_monitoring import get_kpi_health

            health_data = get_kpi_health()

            return Response({
                'success': True,
                'data': health_data,
                'timestamp': timezone.now().isoformat()
            })
        except Exception as e:
            return Response(
                {'error': f'Failed to get health summary: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def check_alerts(self, request):
        """Check all KPIs for threshold breaches and trigger alerts"""
        try:
            from .kpi_monitoring import check_kpi_alerts

            results = check_kpi_alerts()

            return Response({
                'success': True,
                'message': f'KPI monitoring completed: {results["alerts_triggered"]} alerts triggered',
                'data': results,
                'timestamp': timezone.now().isoformat()
            })
        except Exception as e:
            return Response(
                {'error': f'Failed to check alerts: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def export(self, request):
        """Export KPI data to CSV"""
        import csv
        from django.http import HttpResponse

        # Get filtered KPIs
        kpis = self.get_queryset()

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="kpis_export.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'ID', 'Name', 'Name (Arabic)', 'Category', 'Current Value',
            'Target Value', 'Unit', 'Status', 'Last Updated'
        ])

        for kpi in kpis:
            latest_value = kpi.values.order_by('-period_end').first()
            current_value = latest_value.value if latest_value else 0
            last_updated = latest_value.recorded_at if latest_value else kpi.updated_at

            writer.writerow([
                kpi.id,
                kpi.name,
                kpi.name_ar,
                kpi.category.name,
                current_value,
                kpi.target_value or 0,
                kpi.unit,
                kpi.status,
                last_updated.strftime('%Y-%m-%d %H:%M:%S')
            ])

        return response

    @action(detail=False, methods=['post'])
    def bulk_update(self, request):
        """Bulk update multiple KPIs"""
        kpi_ids = request.data.get('kpi_ids', [])
        update_data = request.data.get('update_data', {})

        if not kpi_ids:
            return Response(
                {'error': 'No KPI IDs provided'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from .models import KPI
            kpis = KPI.objects.filter(id__in=kpi_ids)

            # Validate update data
            allowed_fields = ['status', 'target_value', 'warning_threshold', 'critical_threshold']
            filtered_data = {k: v for k, v in update_data.items() if k in allowed_fields}

            updated_count = kpis.update(**filtered_data)

            return Response({
                'success': True,
                'message': f'Updated {updated_count} KPIs',
                'updated_count': updated_count
            })

        except Exception as e:
            return Response(
                {'error': f'Failed to bulk update KPIs: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def bulk_delete(self, request):
        """Bulk delete multiple KPIs"""
        kpi_ids = request.data.get('kpi_ids', [])

        if not kpi_ids:
            return Response(
                {'error': 'No KPI IDs provided'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from .models import KPI
            kpis = KPI.objects.filter(id__in=kpi_ids)
            deleted_count = kpis.count()
            kpis.delete()

            return Response({
                'success': True,
                'message': f'Deleted {deleted_count} KPIs',
                'deleted_count': deleted_count
            })

        except Exception as e:
            return Response(
                {'error': f'Failed to bulk delete KPIs: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def bulk_activate(self, request):
        """Bulk activate/deactivate multiple KPIs"""
        kpi_ids = request.data.get('kpi_ids', [])
        activate = request.data.get('activate', True)

        if not kpi_ids:
            return Response(
                {'error': 'No KPI IDs provided'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from .models import KPI
            status_value = 'ACTIVE' if activate else 'INACTIVE'
            updated_count = KPI.objects.filter(id__in=kpi_ids).update(status=status_value)

            action = 'activated' if activate else 'deactivated'
            return Response({
                'success': True,
                'message': f'{action.capitalize()} {updated_count} KPIs',
                'updated_count': updated_count
            })

        except Exception as e:
            return Response(
                {'error': f'Failed to bulk {action} KPIs: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def trend(self, request, pk=None):
        """Get trend data for a KPI"""
        kpi = self.get_object()

        # Get recent values for trend calculation
        days = int(request.query_params.get('days', 30))
        start_date = timezone.now() - timedelta(days=days)

        values = kpi.values.filter(
            period_start__gte=start_date
        ).order_by('period_start')

        data_points = []
        for value in values:
            data_points.append({
                'date': value.period_start.date(),
                'value': float(value.value),
                'period_start': value.period_start,
                'period_end': value.period_end
            })

        # Calculate trend
        trend_direction = 'stable'
        change_percentage = 0

        if len(data_points) >= 2:
            first_value = data_points[0]['value']
            last_value = data_points[-1]['value']
            change_percentage = ((last_value - first_value) / first_value) * 100

            if change_percentage > 1:
                trend_direction = 'up'
            elif change_percentage < -1:
                trend_direction = 'down'

        trend_data = {
            'kpi_id': kpi.id,
            'kpi_name': kpi.name,
            'data_points': data_points,
            'trend_direction': trend_direction,
            'change_percentage': round(change_percentage, 2)
        }

        from .serializers import KPITrendDataSerializer
        serializer = KPITrendDataSerializer(trend_data)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def add_value(self, request, pk=None):
        """Add a new value for a KPI"""
        kpi = self.get_object()
        from django.shortcuts import get_object_or_404
        employee = get_object_or_404(Employee, user=request.user)

        data = request.data.copy()
        data['kpi'] = kpi.id
        data['recorded_by'] = employee.id

        from .serializers import KPIValueSerializer
        serializer = KPIValueSerializer(data=data)
        if serializer.is_valid():
            serializer.save()

            # Check for threshold breaches and create alerts
            self._check_thresholds(kpi, serializer.instance)

            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def _check_thresholds(self, kpi, kpi_value):
        """Check if KPI value breaches thresholds and create alerts"""
        value = float(kpi_value.value)

        # Check critical threshold
        if kpi.critical_threshold and (
            (kpi.trend_direction == 'UP' and value < float(kpi.critical_threshold)) or
            (kpi.trend_direction == 'DOWN' and value > float(kpi.critical_threshold))
        ):
            self._create_alert(kpi, kpi_value, 'THRESHOLD_BREACH', 'CRITICAL')

        # Check warning threshold
        elif kpi.warning_threshold and (
            (kpi.trend_direction == 'UP' and value < float(kpi.warning_threshold)) or
            (kpi.trend_direction == 'DOWN' and value > float(kpi.warning_threshold))
        ):
            self._create_alert(kpi, kpi_value, 'THRESHOLD_BREACH', 'HIGH')

    def _create_alert(self, kpi, kpi_value, alert_type, severity):
        """Create a KPI alert"""
        from .models import KPIAlert
        KPIAlert.objects.create(
            kpi=kpi,
            alert_type=alert_type,
            severity=severity,
            title=f"KPI Threshold Breach: {kpi.name}",
            title_ar=f"تجاوز عتبة مؤشر الأداء: {kpi.name_ar}",
            message=f"KPI {kpi.name} has breached the {severity.lower()} threshold with value {kpi_value.value}",
            message_ar=f"مؤشر الأداء {kpi.name_ar} تجاوز العتبة {severity.lower()} بقيمة {kpi_value.value}",
            current_value=kpi_value.value,
            threshold_value=kpi.critical_threshold if severity == 'CRITICAL' else kpi.warning_threshold,
            target_value=kpi.target_value
        )

    @action(detail=True, methods=['post'])
    def calculate_automated(self, request, pk=None):
        """Calculate KPI value using algorithms"""
        kpi = self.get_object()

        if not kpi.is_automated:
            return Response(
                {'error': 'KPI is not configured for automated calculation'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Import algorithm service
        from .kpi_algorithms import kpi_algorithm_service
        calculated_value = kpi_algorithm_service.calculate_automated_kpi(kpi)

        if calculated_value is not None:
            # Save the calculated value
            from django.shortcuts import get_object_or_404
            employee = get_object_or_404(Employee, user=request.user)

            kpi_value = KPIValue.objects.create(
                kpi=kpi,
                value=calculated_value,
                period_start=timezone.now().replace(hour=0, minute=0, second=0, microsecond=0),
                period_end=timezone.now(),
                recorded_by=employee,
                is_estimated=True,
                source_data={'method': 'automated_calculation', 'algorithm': 'kpi_algorithm_service'}
            )

            # Check for anomalies
            anomaly_result = kpi_algorithm_service.detect_anomalies(kpi, calculated_value)

            from .serializers import KPIValueSerializer
            serializer = KPIValueSerializer(kpi_value)

            return Response({
                'kpi_value': serializer.data,
                'calculated_value': float(calculated_value),
                'anomaly_detection': anomaly_result,
                'success': True
            })
        else:
            return Response(
                {'error': 'Failed to calculate automated KPI value'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def predict_values(self, request, pk=None):
        """Predict future KPI values using algorithms"""
        kpi = self.get_object()
        periods = int(request.query_params.get('periods', 6))

        from .kpi_algorithms import kpi_algorithm_service
        predictions = kpi_algorithm_service.predict_future_values(kpi, periods)

        return Response({
            'kpi_id': kpi.id,
            'kpi_name': kpi.name,
            'predictions': predictions,
            'algorithm': 'time_series_analysis'
        })

    @action(detail=True, methods=['get'])
    def analyze_trend(self, request, pk=None):
        """Analyze KPI trend using algorithms"""
        kpi = self.get_object()
        periods = int(request.query_params.get('periods', 12))

        from .kpi_algorithms import kpi_algorithm_service
        trend_analysis = kpi_algorithm_service.analyze_trend(kpi, periods)

        return Response({
            'kpi_id': kpi.id,
            'kpi_name': kpi.name,
            'trend_analysis': trend_analysis
        })

    @action(detail=True, methods=['get'])
    def get_recommendations(self, request, pk=None):
        """Get AI-powered recommendations for KPI improvement"""
        kpi = self.get_object()

        from .kpi_algorithms import kpi_algorithm_service
        recommendations = kpi_algorithm_service.generate_recommendations(kpi)

        return Response({
            'kpi_id': kpi.id,
            'kpi_name': kpi.name,
            'recommendations': recommendations,
            'generated_at': timezone.now()
        })

    @action(detail=True, methods=['post'])
    def detect_anomaly(self, request, pk=None):
        """Detect anomalies in KPI value"""
        kpi = self.get_object()
        value = request.data.get('value')

        if value is None:
            return Response(
                {'error': 'Value is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from decimal import Decimal
            value_decimal = Decimal(str(value))

            from .kpi_algorithms import kpi_algorithm_service
            anomaly_result = kpi_algorithm_service.detect_anomalies(kpi, value_decimal)

            return Response({
                'kpi_id': kpi.id,
                'value': float(value_decimal),
                'anomaly_detection': anomaly_result
            })
        except (ValueError, TypeError):
            return Response(
                {'error': 'Invalid value format'},
                status=status.HTTP_400_BAD_REQUEST
            )


class KPIValueViewSet(viewsets.ReadOnlyModelViewSet):
    """
    DEPRECATED: Legacy KPI Value ViewSet - Read-only access only.
    Use EnhancedKPIValueViewSet for all KPI value operations.
    Manual KPI value entry has been disabled to enforce enterprise automation.
    All KPI values must be calculated automatically from operational data.
    """
    queryset = None
    serializer_class = None
    permission_classes = [IsAuthenticated]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from .models import KPIValue
        from .serializers import KPIValueSerializer
        self.queryset = KPIValue.objects.all()
        self.serializer_class = KPIValueSerializer

    def get_queryset(self):
        from .models import KPIValue
        queryset = KPIValue.objects.select_related(
            'kpi', 'recorded_by', 'department', 'project', 'employee'
        )

        # Filter by KPI
        kpi_id = self.request.query_params.get('kpi')
        if kpi_id:
            queryset = queryset.filter(kpi_id=kpi_id)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date:
            queryset = queryset.filter(period_start__gte=start_date)
        if end_date:
            queryset = queryset.filter(period_end__lte=end_date)

        return queryset.order_by('-period_start')


class KPITargetViewSet(viewsets.ModelViewSet):
    queryset = None
    serializer_class = None
    permission_classes = [IsAuthenticated]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from .models import KPITarget
        from .serializers import KPITargetSerializer
        self.queryset = KPITarget.objects.all()
        self.serializer_class = KPITargetSerializer

    def get_queryset(self):
        from .models import KPITarget
        queryset = KPITarget.objects.select_related(
            'kpi', 'created_by', 'department', 'project', 'employee'
        )

        # Filter by KPI
        kpi_id = self.request.query_params.get('kpi')
        if kpi_id:
            queryset = queryset.filter(kpi_id=kpi_id)

        # Filter by active targets
        active_only = self.request.query_params.get('active_only', 'false').lower() == 'true'
        if active_only:
            today = date.today()
            queryset = queryset.filter(start_date__lte=today, end_date__gte=today)

        return queryset.order_by('-start_date')

    def perform_create(self, serializer):
        from django.shortcuts import get_object_or_404
        employee = get_object_or_404(Employee, user=self.request.user)
        serializer.save(created_by=employee)


class KPIAlertViewSet(viewsets.ModelViewSet):
    queryset = None
    serializer_class = None
    permission_classes = [IsAuthenticated]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from .models import KPIAlert
        from .serializers import KPIAlertSerializer
        self.queryset = KPIAlert.objects.all()
        self.serializer_class = KPIAlertSerializer

    def get_queryset(self):
        from .models import KPIAlert
        queryset = KPIAlert.objects.select_related(
            'kpi', 'acknowledged_by', 'resolved_by'
        )

        # Filter by status
        status_filter = self.request.query_params.get('status', 'ACTIVE')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by severity
        severity = self.request.query_params.get('severity')
        if severity:
            queryset = queryset.filter(severity=severity)

        return queryset.order_by('-created_at')

    @action(detail=True, methods=['post'])
    def acknowledge(self, request, pk=None):
        """Acknowledge an alert"""
        alert = self.get_object()
        from django.shortcuts import get_object_or_404
        employee = get_object_or_404(Employee, user=request.user)

        alert.status = 'ACKNOWLEDGED'
        alert.acknowledged_by = employee
        alert.acknowledged_at = timezone.now()
        alert.save()

        serializer = self.get_serializer(alert)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        """Resolve an alert"""
        alert = self.get_object()
        from django.shortcuts import get_object_or_404
        employee = get_object_or_404(Employee, user=request.user)

        alert.status = 'RESOLVED'
        alert.resolved_by = employee
        alert.resolved_at = timezone.now()
        alert.resolution_notes = request.data.get('resolution_notes', '')
        alert.save()

        serializer = self.get_serializer(alert)
        return Response(serializer.data)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def kpi_dashboard(request):
    """
    Get comprehensive KPI dashboard data with real calculations
    """
    try:
        from .models import KPI, KPICategory, KPIValue, KPIAlert
        from .serializers import KPICategorySerializer, KPISerializer
        from django.db.models import Count, Q, Avg
        from decimal import Decimal

        # Get active KPIs with their latest values
        kpis = KPI.objects.filter(status='ACTIVE').select_related('category', 'created_by').prefetch_related('values')

        # Filter by user role permissions
        user_profile = getattr(request.user, 'userprofile', None)
        if user_profile and user_profile.role:
            kpis = kpis.filter(
                Q(visible_to_roles=user_profile.role) |
                Q(visible_to_roles__isnull=True)
            )

        # Get categories
        categories = KPICategory.objects.filter(is_active=True).order_by('sort_order')

        # Calculate real KPI performance metrics
        total_kpis = kpis.count()
        kpis_on_target = 0
        kpis_above_target = 0
        kpis_below_target = 0

        top_performing_kpis = []
        underperforming_kpis = []

        for kpi in kpis:
            # Get latest KPI value
            latest_value = kpi.values.order_by('-period_end').first()
            current_value = float(latest_value.value) if latest_value else 0
            target_value = float(kpi.target_value) if kpi.target_value else 0

            if target_value > 0:
                achievement_percentage = (current_value / target_value) * 100

                # Categorize performance
                if 95 <= achievement_percentage <= 105:  # Within 5% of target
                    kpis_on_target += 1
                elif achievement_percentage > 105:
                    kpis_above_target += 1
                    if achievement_percentage > 120:  # Top performers
                        top_performing_kpis.append(kpi)
                else:
                    kpis_below_target += 1
                    if achievement_percentage < 80:  # Underperformers
                        underperforming_kpis.append(kpi)

        # Get active alerts
        active_alerts = KPIAlert.objects.filter(status='ACTIVE').count()
        critical_alerts = KPIAlert.objects.filter(status='ACTIVE', severity='CRITICAL').count()

        kpi_summary = {
            'total_kpis': total_kpis,
            'active_kpis': total_kpis,
            'kpis_on_target': kpis_on_target,
            'kpis_above_target': kpis_above_target,
            'kpis_below_target': kpis_below_target,
            'active_alerts': active_alerts,
            'critical_alerts': critical_alerts,
            'categories_count': categories.count(),
            'last_updated': timezone.now().isoformat()
        }

        # Get recent alerts
        recent_alerts = KPIAlert.objects.filter(status='ACTIVE').order_by('-created_at')[:5]

        # Calculate trends for all KPIs
        kpis_with_trends = []
        for kpi in kpis:
            kpi_data = KPISerializer(kpi).data

            # Calculate trend from last 30 days
            from datetime import timedelta
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=30)

            values = kpi.values.filter(
                period_start__gte=start_date,
                period_end__lte=end_date
            ).order_by('period_start')

            if values.count() >= 2:
                first_value = float(values.first().value)
                last_value = float(values.last().value)

                if first_value != 0:
                    change_percentage = ((last_value - first_value) / first_value) * 100

                    if change_percentage > 5:
                        trend_direction = 'up'
                    elif change_percentage < -5:
                        trend_direction = 'down'
                    else:
                        trend_direction = 'stable'
                else:
                    change_percentage = 0
                    trend_direction = 'stable'
            else:
                change_percentage = 0
                trend_direction = 'stable'

            kpi_data['change_percentage'] = round(change_percentage, 2)
            kpi_data['trend'] = trend_direction
            kpis_with_trends.append(kpi_data)

        # Serialize data
        dashboard_data = {
            'categories': KPICategorySerializer(categories, many=True).data,
            'recent_alerts': [
                {
                    'id': alert.id,
                    'kpi': alert.kpi.id,
                    'kpi_name': alert.kpi.name,
                    'severity': alert.severity,
                    'message': alert.message,
                    'message_ar': alert.message_ar,
                    'status': alert.status,
                    'created_at': alert.created_at.isoformat()
                } for alert in recent_alerts
            ],
            'top_performing_kpis': KPISerializer(top_performing_kpis[:5], many=True).data,
            'underperforming_kpis': KPISerializer(underperforming_kpis[:5], many=True).data,
            'kpi_summary': kpi_summary,
            'all_kpis': kpis_with_trends
        }

        return Response(dashboard_data)

    except Exception as e:
        import traceback
        return Response(
            {'error': f'Failed to load KPI dashboard: {str(e)}', 'traceback': traceback.format_exc()},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# Import new models
from .models import PerformanceReview, PayrollPeriod, PayrollEntry, JobPosting, TrainingProgram, Invoice, CostCenter

# New ViewSets for missing endpoints

# Performance Review ViewSet
class PerformanceReviewViewSet(viewsets.ModelViewSet):
    queryset = PerformanceReview.objects.all()
    serializer_class = ReportSerializer  # Using Report serializer as placeholder
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['employee', 'reviewer', 'review_type', 'is_final']
    search_fields = ['employee__user__first_name', 'employee__user__last_name']
    ordering_fields = ['review_period_start', 'review_period_end', 'created_at']
    ordering = ['-review_period_end']


# Payroll ViewSets
class PayrollViewSet(viewsets.ModelViewSet):
    # CRITICAL FIX: Add select_related to prevent N+1 queries
    queryset = PayrollEntry.objects.select_related('employee__user', 'payroll_period').all()
    serializer_class = ReportSerializer  # Using Report serializer as placeholder
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['employee', 'payroll_period', 'is_paid']
    search_fields = ['employee__user__first_name', 'employee__user__last_name']
    ordering_fields = ['created_at', 'net_salary']
    ordering = ['-created_at']


# Recruitment ViewSets
class RecruitmentViewSet(viewsets.ModelViewSet):
    # CRITICAL FIX: Add select_related to prevent N+1 queries
    queryset = JobPosting.objects.select_related('department', 'hiring_manager__user').all()
    serializer_class = ReportSerializer  # Using Report serializer as placeholder
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'status', 'employment_type']
    search_fields = ['title', 'title_ar', 'description']
    ordering_fields = ['posted_date', 'closing_date', 'created_at']
    ordering = ['-posted_date']


# Training ViewSet
class TrainingViewSet(viewsets.ModelViewSet):
    queryset = TrainingProgram.objects.all()
    serializer_class = ReportSerializer  # Using Report serializer as placeholder
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'status', 'is_mandatory']
    search_fields = ['title', 'title_ar', 'description', 'instructor']
    ordering_fields = ['start_date', 'end_date', 'created_at']
    ordering = ['-start_date']


# Invoice ViewSet
class InvoiceViewSet(viewsets.ModelViewSet):
    queryset = Invoice.objects.all()
    serializer_class = ExpenseSerializer  # Using Expense serializer as placeholder
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['customer', 'status']
    search_fields = ['invoice_number', 'customer__first_name', 'customer__last_name']
    ordering_fields = ['issue_date', 'due_date', 'total_amount', 'created_at']
    ordering = ['-created_at']


# Cost Center ViewSet
class CostCenterViewSet(viewsets.ModelViewSet):
    # CRITICAL FIX: Add select_related to prevent N+1 queries
    queryset = CostCenter.objects.select_related('department', 'manager__user').all()
    serializer_class = DepartmentSerializer  # Using Department serializer as placeholder
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'manager', 'is_active']
    search_fields = ['code', 'name', 'name_ar']
    ordering_fields = ['code', 'name', 'budget_allocated', 'created_at']
    ordering = ['code']


# Security monitoring helper functions
def calculate_security_score():
    """Calculate real-time security score based on system metrics"""
    try:
        score = 100

        # Check for recent failed logins (reduce score if too many)
        from django.contrib.admin.models import LogEntry
        from datetime import datetime, timedelta

        recent_failures = LogEntry.objects.filter(
            action_time__gte=datetime.now() - timedelta(hours=24),
            action_flag=3  # DELETION flag often used for failed attempts
        ).count()

        if recent_failures > 10:
            score -= min(30, recent_failures * 2)

        # Check for inactive users (security risk)
        inactive_users = User.objects.filter(is_active=False).count()
        total_users = User.objects.count()

        if total_users > 0:
            inactive_ratio = inactive_users / total_users
            if inactive_ratio > 0.2:  # More than 20% inactive
                score -= 10

        # Check for users without proper roles
        users_without_profiles = User.objects.filter(userprofile__isnull=True).count()
        if users_without_profiles > 0:
            score -= min(20, users_without_profiles * 5)

        return max(0, min(100, score))

    except Exception:
        return 0


def get_active_threats_count():
    """Get count of active security threats"""
    try:
        # In a real implementation, this would connect to security monitoring
        # For now, return 0 as we don't have active threat detection
        return 0
    except Exception:
        return 0


def get_blocked_attacks_count():
    """Get count of blocked attacks in the last 24 hours"""
    try:
        # In a real implementation, this would read from security logs
        # For now, calculate based on failed login attempts as a proxy
        from django.contrib.admin.models import LogEntry
        from datetime import datetime, timedelta

        blocked_count = LogEntry.objects.filter(
            action_time__gte=datetime.now() - timedelta(hours=24),
            action_flag=3  # Failed attempts
        ).count()

        return blocked_count

    except Exception:
        return 0


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def financial_analytics(request):
    """
    Get financial analytics and trends
    """
    try:
        from datetime import datetime, timedelta
        from django.db.models import Sum, Avg, Count

        # Calculate date ranges
        now = datetime.now()
        current_month = now.replace(day=1)
        last_month = (current_month - timedelta(days=1)).replace(day=1)
        current_year = now.replace(month=1, day=1)
        last_year = current_year.replace(year=current_year.year - 1)

        # Current month expenses
        current_month_expenses = Expense.objects.filter(
            expense_date__gte=current_month,
            status='APPROVED'
        ).aggregate(total=Sum('amount'))['total'] or 0

        # Last month expenses
        last_month_expenses = Expense.objects.filter(
            expense_date__gte=last_month,
            expense_date__lt=current_month,
            status='APPROVED'
        ).aggregate(total=Sum('amount'))['total'] or 0

        # Calculate growth rate
        expense_growth_rate = 0
        if last_month_expenses > 0:
            expense_growth_rate = ((current_month_expenses - last_month_expenses) / last_month_expenses) * 100

        # Budget utilization
        total_budgets = Budget.objects.filter(is_active=True).aggregate(total=Sum('allocated_amount'))['total'] or 0
        budget_utilization = 0
        if total_budgets > 0:
            budget_utilization = (current_month_expenses / total_budgets) * 100

        # Department-wise expenses
        dept_expenses = Expense.objects.filter(
            expense_date__gte=current_month,
            status='APPROVED'
        ).values('category').annotate(
            total=Sum('amount'),
            count=Count('id')
        ).order_by('-total')[:5]

        data = {
            'current_month_expenses': float(current_month_expenses),
            'last_month_expenses': float(last_month_expenses),
            'expense_growth_rate': round(expense_growth_rate, 2),
            'budget_utilization': round(budget_utilization, 2),
            'total_budgets': float(total_budgets),
            'department_expenses': list(dept_expenses),
            'generated_at': now.isoformat()
        }

        return Response(data)

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error in financial_analytics: {str(e)}")
        return Response(
            {'error': 'Failed to fetch financial analytics'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

# FIXED: Employee Dashboard API endpoints
@api_view(['GET'])
@permission_classes([AllowAny])
def my_meetings(request):
    """
    Get current user's upcoming meetings
    """
    try:
        # For now, return empty array since we don't have meeting data
        # This should be replaced with real meeting data when available
        meetings = []

        return Response(meetings)

    except Exception as e:
        return Response(
            {'error': f'Failed to get meetings: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([AllowAny])
def recent_messages(request):
    """
    Get current user's recent messages
    """
    try:
        # For now, return empty array since we don't have message data
        # This should be replaced with real message data when available
        messages = []

        return Response(messages)

    except Exception as e:
        return Response(
            {'error': f'Failed to get messages: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

class EmployeeTaskViewSet(viewsets.ModelViewSet):
    """
    Employee Tasks ViewSet - provides tasks filtered for the current employee
    This is the missing /api/employee-tasks/ endpoint that the frontend expects
    """
    serializer_class = TaskSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'description', 'project__name', 'status']
    filterset_fields = ['status', 'priority', 'project']
    ordering_fields = ['created_at', 'due_date', 'priority', 'status']
    ordering = ['-created_at']

    def get_queryset(self):
        """Filter tasks for the current employee only"""
        try:
            # Get the employee record for the current user
            employee = Employee.objects.get(user=self.request.user)
            # Return tasks assigned to this employee
            return Task.objects.select_related(
                'project', 'assigned_to__user', 'created_by__user'
            ).filter(assigned_to=employee)
        except Employee.DoesNotExist:
            # If no employee record found, return empty queryset
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"No employee record found for user {self.request.user.username}")
            return Task.objects.none()
        except Exception as e:
            # Handle any other errors gracefully
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in EmployeeTaskViewSet.get_queryset for user {self.request.user.username}: {str(e)}")
            return Task.objects.none()

    def perform_create(self, serializer):
        """Set the created_by field when creating a task"""
        try:
            employee = Employee.objects.get(user=self.request.user)
            serializer.save(created_by=employee)
        except Employee.DoesNotExist:
            # If no employee found, save without created_by
            serializer.save(created_by=None)

class PersonalMessageViewSet(viewsets.ModelViewSet):
    """
    Personal Messages ViewSet - provides messages filtered for the current user
    This is the missing /api/personal-messages/ endpoint that the frontend expects
    """
    serializer_class = MessageSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['subject', 'content', 'sender__user__first_name', 'sender__user__last_name']
    filterset_fields = ['is_read', 'is_important']  # FIXED: Use actual Message model fields
    ordering_fields = ['sent_at', 'is_read', 'is_important']  # FIXED: Use actual Message model fields
    ordering = ['-sent_at']  # FIXED: Use actual Message model field

    def get_queryset(self):
        """Filter messages for the current user (sent or received)"""
        try:
            # Get the employee record for the current user
            employee = Employee.objects.get(user=self.request.user)
            # Return messages where user is sender or recipient
            from django.db.models import Q
            return Message.objects.select_related(
                'sender__user', 'recipient__user'
            ).filter(
                Q(sender=employee) | Q(recipient=employee)
            )
        except Employee.DoesNotExist:
            # If no employee record found, return empty queryset
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"No employee record found for user {self.request.user.username}")
            return Message.objects.none()
        except Exception as e:
            # Handle any other errors gracefully
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in PersonalMessageViewSet.get_queryset for user {self.request.user.username}: {str(e)}")
            return Message.objects.none()

    def perform_create(self, serializer):
        """Set the sender field when creating a message"""
        try:
            employee = Employee.objects.get(user=self.request.user)
            serializer.save(sender=employee)
        except Employee.DoesNotExist:
            # If no employee found, raise a proper error
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"No employee record found for user {self.request.user.username} when creating message")
            from rest_framework.exceptions import ValidationError
            raise ValidationError("Employee record not found. Cannot create message.")

@api_view(['GET'])
@permission_classes([AllowAny])
def my_tasks_dashboard(request):
    """
    Get current user's tasks for dashboard
    """
    try:
        user = request.user

        # Get tasks assigned to current user from Task model
        try:
            tasks = Task.objects.filter(assigned_to=user).order_by('-created_at')[:10]

            task_data = []
            for task in tasks:
                task_data.append({
                    'id': task.id,
                    'title': task.title,
                    'description': task.description,
                    'status': task.status,
                    'priority': task.priority,
                    'due_date': task.due_date,
                    'created_at': task.created_at,
                    'project': task.project.name if hasattr(task, 'project') and task.project else None,
                })

            return Response(task_data)
        except Exception:
            # If Task model doesn't exist or has issues, return empty array
            return Response([])

    except Exception as e:
        return Response(
            {'error': f'Failed to get tasks: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# Financial Management ViewSets - General Ledger
class AccountTypeViewSet(viewsets.ModelViewSet):
    queryset = AccountType.objects.all()
    serializer_class = AccountTypeSerializer
    permission_classes = [IsAuthenticated]

class ChartOfAccountsViewSet(viewsets.ModelViewSet):
    queryset = ChartOfAccounts.objects.select_related('account_type', 'parent_account').all()
    serializer_class = ChartOfAccountsSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def hierarchy(self, request):
        """Get accounts in hierarchical structure"""
        accounts = self.get_queryset().filter(parent_account__isnull=True)
        serializer = self.get_serializer(accounts, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def balance_history(self, request, pk=None):
        """Get account balance history"""
        account = self.get_object()
        # This would typically include date range parameters
        # For now, return current balance
        return Response({
            'account_code': account.account_code,
            'account_name': account.account_name,
            'current_balance': account.get_balance(),
            'balance_history': []  # Would be populated with historical data
        })

class FiscalYearViewSet(viewsets.ModelViewSet):
    queryset = FiscalYear.objects.all()
    serializer_class = FiscalYearSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get current fiscal year"""
        try:
            current_fy = FiscalYear.objects.get(is_current=True)
            serializer = self.get_serializer(current_fy)
            return Response(serializer.data)
        except FiscalYear.DoesNotExist:
            return Response({'error': 'No current fiscal year set'}, status=404)

class JournalEntryBatchViewSet(viewsets.ModelViewSet):
    queryset = JournalEntryBatch.objects.select_related('created_by__user', 'posted_by__user').all()
    serializer_class = JournalEntryBatchSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=True, methods=['post'])
    def post_batch(self, request, pk=None):
        """Post a journal entry batch"""
        batch = self.get_object()
        if batch.is_posted:
            return Response({'error': 'Batch already posted'}, status=400)

        if not batch.is_balanced():
            return Response({'error': 'Batch is not balanced'}, status=400)

        employee = Employee.objects.get(user=request.user)
        batch.is_posted = True
        batch.posted_at = timezone.now()
        batch.posted_by = employee
        batch.save()

        # Update all journal entries in the batch
        batch.journal_entries.update(is_posted=True, posted_at=timezone.now())

        return Response({'message': 'Batch posted successfully'})

class JournalEntryViewSet(viewsets.ModelViewSet):
    queryset = JournalEntry.objects.select_related(
        'batch', 'account', 'department', 'project', 'employee__user'
    ).all()
    serializer_class = JournalEntrySerializer
    permission_classes = [IsAuthenticated]


# Accounts Payable ViewSets
class VendorViewSet(viewsets.ModelViewSet):
    queryset = Vendor.objects.all()
    serializer_class = VendorSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=True, methods=['get'])
    def outstanding_invoices(self, request, pk=None):
        """Get vendor's outstanding invoices"""
        vendor = self.get_object()
        invoices = vendor.vendor_invoices.filter(status__in=['PENDING', 'APPROVED'])
        serializer = VendorInvoiceSerializer(invoices, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def aging_report(self, request):
        """Get vendor aging report"""
        from django.db.models import Sum, Case, When, DecimalField
        from datetime import timedelta

        today = timezone.now().date()
        vendors = Vendor.objects.annotate(
            current=Sum(Case(
                When(vendor_invoices__due_date__gte=today, then='vendor_invoices__total_amount'),
                default=0, output_field=DecimalField()
            )),
            days_30=Sum(Case(
                When(vendor_invoices__due_date__lt=today,
                     vendor_invoices__due_date__gte=today - timedelta(days=30),
                     then='vendor_invoices__total_amount'),
                default=0, output_field=DecimalField()
            )),
            days_60=Sum(Case(
                When(vendor_invoices__due_date__lt=today - timedelta(days=30),
                     vendor_invoices__due_date__gte=today - timedelta(days=60),
                     then='vendor_invoices__total_amount'),
                default=0, output_field=DecimalField()
            )),
            days_90_plus=Sum(Case(
                When(vendor_invoices__due_date__lt=today - timedelta(days=60),
                     then='vendor_invoices__total_amount'),
                default=0, output_field=DecimalField()
            ))
        ).filter(is_active=True)

        aging_data = []
        for vendor in vendors:
            aging_data.append({
                'vendor_id': vendor.id,
                'vendor_name': vendor.company_name,
                'current': float(vendor.current or 0),
                '1-30_days': float(vendor.days_30 or 0),
                '31-60_days': float(vendor.days_60 or 0),
                '60+_days': float(vendor.days_90_plus or 0),
                'total': float((vendor.current or 0) + (vendor.days_30 or 0) +
                              (vendor.days_60 or 0) + (vendor.days_90_plus or 0))
            })

        return Response(aging_data)

class VendorInvoiceViewSet(viewsets.ModelViewSet):
    queryset = VendorInvoice.objects.select_related(
        'vendor', 'department', 'project', 'approved_by__user', 'created_by__user'
    ).all()
    serializer_class = VendorInvoiceSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve vendor invoice"""
        invoice = self.get_object()
        if invoice.status != 'PENDING':
            return Response({'error': 'Invoice is not pending approval'}, status=400)

        employee = Employee.objects.get(user=request.user)
        invoice.status = 'APPROVED'
        invoice.approved_by = employee
        invoice.approval_date = timezone.now()
        invoice.save()

        return Response({'message': 'Invoice approved successfully'})

    @action(detail=False, methods=['get'])
    def dashboard_summary(self, request):
        """Get AP dashboard summary"""
        from django.db.models import Sum, Count, Q
        from datetime import timedelta

        today = timezone.now().date()

        # Total invoices by status
        total_invoices = self.get_queryset().count()
        pending_invoices = self.get_queryset().filter(status='PENDING').count()
        approved_invoices = self.get_queryset().filter(status='APPROVED').count()
        overdue_invoices = self.get_queryset().filter(
            due_date__lt=today,
            status__in=['PENDING', 'APPROVED']
        ).count()

        # Amount summaries
        total_outstanding = self.get_queryset().filter(
            status__in=['PENDING', 'APPROVED']
        ).aggregate(Sum('total_amount'))['total_amount__sum'] or 0

        overdue_amount = self.get_queryset().filter(
            due_date__lt=today,
            status__in=['PENDING', 'APPROVED']
        ).aggregate(Sum('total_amount'))['total_amount__sum'] or 0

        # Due this week
        week_end = today + timedelta(days=7)
        due_this_week = self.get_queryset().filter(
            due_date__gte=today,
            due_date__lte=week_end,
            status__in=['PENDING', 'APPROVED']
        ).aggregate(
            count=Count('id'),
            amount=Sum('total_amount')
        )

        return Response({
            'total_invoices': total_invoices,
            'pending_invoices': pending_invoices,
            'approved_invoices': approved_invoices,
            'overdue_invoices': overdue_invoices,
            'total_outstanding': float(total_outstanding),
            'overdue_amount': float(overdue_amount),
            'due_this_week': {
                'count': due_this_week['count'] or 0,
                'amount': float(due_this_week['amount'] or 0)
            }
        })

    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue vendor invoices for collections"""
        from datetime import date

        today = date.today()
        overdue_invoices = self.get_queryset().filter(
            due_date__lt=today,
            status__in=['PENDING', 'APPROVED']
        ).select_related('vendor').order_by('due_date')

        # Add days overdue calculation
        for invoice in overdue_invoices:
            invoice.days_overdue = (today - invoice.due_date).days

        serializer = self.get_serializer(overdue_invoices, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue invoices"""
        today = timezone.now().date()
        overdue_invoices = self.get_queryset().filter(
            due_date__lt=today,
            status__in=['PENDING', 'APPROVED']
        )
        serializer = self.get_serializer(overdue_invoices, many=True)
        return Response(serializer.data)


# Accounts Receivable ViewSets
class CustomerInvoiceViewSet(viewsets.ModelViewSet):
    queryset = CustomerInvoice.objects.select_related(
        'customer', 'department', 'project', 'created_by__user'
    ).all()
    serializer_class = CustomerInvoiceSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=True, methods=['post'])
    def send(self, request, pk=None):
        """Send invoice to customer"""
        invoice = self.get_object()
        if invoice.status == 'DRAFT':
            invoice.status = 'SENT'
            invoice.sent_date = timezone.now().date()
            invoice.save()
            return Response({'message': 'Invoice sent successfully'})
        return Response({'error': 'Invoice cannot be sent'}, status=400)

    @action(detail=False, methods=['get'])
    def aging_report(self, request):
        """Get customer aging report"""
        from django.db.models import Sum, Case, When, DecimalField
        from datetime import timedelta

        today = timezone.now().date()
        customers = Customer.objects.annotate(
            current=Sum(Case(
                When(customer_invoices__due_date__gte=today, then='customer_invoices__total_amount'),
                default=0, output_field=DecimalField()
            )),
            days_30=Sum(Case(
                When(customer_invoices__due_date__lt=today,
                     customer_invoices__due_date__gte=today - timedelta(days=30),
                     then='customer_invoices__total_amount'),
                default=0, output_field=DecimalField()
            )),
            days_60=Sum(Case(
                When(customer_invoices__due_date__lt=today - timedelta(days=30),
                     customer_invoices__due_date__gte=today - timedelta(days=60),
                     then='customer_invoices__total_amount'),
                default=0, output_field=DecimalField()
            )),
            days_90_plus=Sum(Case(
                When(customer_invoices__due_date__lt=today - timedelta(days=60),
                     then='customer_invoices__total_amount'),
                default=0, output_field=DecimalField()
            ))
        )

        aging_data = []
        for customer in customers:
            aging_data.append({
                'customer_id': customer.id,
                'customer_name': customer.name,
                'current': float(customer.current or 0),
                '1-30_days': float(customer.days_30 or 0),
                '31-60_days': float(customer.days_60 or 0),
                '60+_days': float(customer.days_90_plus or 0),
                'total': float((customer.current or 0) + (customer.days_30 or 0) +
                              (customer.days_60 or 0) + (customer.days_90_plus or 0))
            })

        return Response(aging_data)

    @action(detail=False, methods=['get'])
    def collections(self, request):
        """Get overdue customer invoices for collections with priority"""
        from datetime import date

        today = date.today()
        overdue_invoices = self.get_queryset().filter(
            due_date__lt=today,
            status__in=['SENT', 'PARTIAL']
        ).select_related('customer').order_by('due_date')

        # Add days overdue calculation and collection priority
        collection_data = []
        for invoice in overdue_invoices:
            days_overdue = (today - invoice.due_date).days

            # Determine priority based on days overdue and amount
            if days_overdue > 90 or invoice.remaining_balance > 50000:
                priority = 'URGENT'
            elif days_overdue > 60 or invoice.remaining_balance > 20000:
                priority = 'HIGH'
            elif days_overdue > 30:
                priority = 'MEDIUM'
            else:
                priority = 'LOW'

            collection_data.append({
                'id': invoice.id,
                'invoice_number': invoice.invoice_number,
                'customer_id': invoice.customer.id,
                'customer_name': f"{invoice.customer.first_name} {invoice.customer.last_name}".strip(),
                'customer_email': invoice.customer.email,
                'customer_phone': getattr(invoice.customer, 'phone', ''),
                'total_amount': float(invoice.total_amount),
                'remaining_balance': float(invoice.remaining_balance),
                'days_overdue': days_overdue,
                'due_date': invoice.due_date.isoformat(),
                'priority': priority,
                'collection_status': 'NEW',  # Default status
                'payment_plan': False
            })

        return Response(collection_data)

    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue customer invoices"""
        today = timezone.now().date()
        overdue_invoices = self.get_queryset().filter(
            due_date__lt=today,
            status__in=['SENT', 'PARTIAL']
        )
        serializer = self.get_serializer(overdue_invoices, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def dashboard_summary(self, request):
        """Get AR dashboard summary"""
        from django.db.models import Sum, Count, Q
        from datetime import timedelta

        today = timezone.now().date()

        # Total invoices by status
        total_invoices = self.get_queryset().count()
        sent_invoices = self.get_queryset().filter(status__in=['SENT', 'PAID', 'PARTIAL']).count()
        paid_invoices = self.get_queryset().filter(status='PAID').count()
        overdue_invoices = self.get_queryset().filter(
            due_date__lt=today,
            status__in=['SENT', 'PARTIAL']
        ).count()

        # Amount summaries
        total_outstanding = self.get_queryset().filter(
            status__in=['SENT', 'PARTIAL']
        ).aggregate(Sum('remaining_balance'))['remaining_balance__sum'] or 0

        overdue_amount = self.get_queryset().filter(
            due_date__lt=today,
            status__in=['SENT', 'PARTIAL']
        ).aggregate(Sum('remaining_balance'))['remaining_balance__sum'] or 0

        # Collection rate
        total_billed = self.get_queryset().aggregate(Sum('total_amount'))['total_amount__sum'] or 0
        total_collected = self.get_queryset().aggregate(Sum('paid_amount'))['paid_amount__sum'] or 0
        collection_rate = (total_collected / total_billed * 100) if total_billed > 0 else 0

        return Response({
            'total_invoices': total_invoices,
            'sent_invoices': sent_invoices,
            'paid_invoices': paid_invoices,
            'overdue_invoices': overdue_invoices,
            'total_outstanding': float(total_outstanding),
            'overdue_amount': float(overdue_amount),
            'collection_rate': round(collection_rate, 2)
        })


class PaymentViewSet(viewsets.ModelViewSet):
    queryset = Payment.objects.select_related(
        'vendor_invoice__vendor', 'customer_invoice__customer', 'created_by__user'
    ).all()
    serializer_class = PaymentSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        payment = serializer.save(created_by=employee)

        # Update invoice paid amount
        if payment.vendor_invoice:
            invoice = payment.vendor_invoice
            invoice.paid_amount += payment.amount
            if invoice.paid_amount >= invoice.total_amount:
                invoice.status = 'PAID'
            invoice.save()

        elif payment.customer_invoice:
            invoice = payment.customer_invoice
            invoice.paid_amount += payment.amount
            if invoice.paid_amount >= invoice.total_amount:
                invoice.status = 'PAID'
            elif invoice.paid_amount > 0:
                invoice.status = 'PARTIAL'
            invoice.save()

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get payment summary"""
        from django.db.models import Sum

        vendor_payments = self.get_queryset().filter(
            payment_type='VENDOR_PAYMENT'
        ).aggregate(total=Sum('amount'))['total'] or 0

        customer_payments = self.get_queryset().filter(
            payment_type='CUSTOMER_PAYMENT'
        ).aggregate(total=Sum('amount'))['total'] or 0

        return Response({
            'vendor_payments': float(vendor_payments),
            'customer_payments': float(customer_payments),
            'net_cash_flow': float(customer_payments - vendor_payments)
        })


class FinancialReportsViewSet(viewsets.ViewSet):
    """
    Financial Reports API - Generates comprehensive financial statements
    """
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """Get financial reports dashboard summary"""
        from django.db.models import Sum, Count, Q
        from datetime import date, timedelta

        today = date.today()

        # Mock data for now - in production, this would calculate from actual data
        dashboard_data = {
            'profit_loss': {
                'name': 'Profit & Loss Report',
                'description': 'Revenue, expenses, and profitability analysis',
                'last_generated': today.isoformat(),
                'status': 'READY',
                'key_metrics': {
                    'total_revenue': 250000,
                    'net_income': 45000,
                    'gross_profit_margin': 35.5
                }
            },
            'balance_sheet': {
                'name': 'Balance Sheet Report',
                'description': 'Assets, liabilities, and equity position',
                'last_generated': today.isoformat(),
                'status': 'READY',
                'key_metrics': {
                    'total_assets': 850000,
                    'total_liabilities': 320000,
                    'total_equity': 530000
                }
            },
            'cash_flow': {
                'name': 'Cash Flow Report',
                'description': 'Operating, investing, and financing cash flows',
                'last_generated': today.isoformat(),
                'status': 'READY',
                'key_metrics': {
                    'operating_cash_flow': 65000,
                    'investing_cash_flow': -25000,
                    'financing_cash_flow': 15000
                }
            },
            'aging_reports': {
                'name': 'Aging Reports',
                'description': 'Accounts receivable and payable aging analysis',
                'last_generated': today.isoformat(),
                'status': 'READY',
                'key_metrics': {
                    'total_ar': 125000,
                    'overdue_ar': 35000,
                    'total_ap': 85000
                }
            },
            'financial_ratios': {
                'current_ratio': 2.15,
                'quick_ratio': 1.85,
                'debt_to_equity': 0.60,
                'gross_profit_margin': 35.5,
                'net_profit_margin': 18.0,
                'return_on_assets': 12.5
            },
            'period_comparison': {
                'revenue_growth': 15.2,
                'expense_growth': 8.5,
                'profit_growth': 22.8,
                'cash_growth': 12.1
            }
        }

        return Response(dashboard_data)

    @action(detail=False, methods=['get'])
    def profit_loss(self, request):
        """Generate Profit & Loss Report"""
        period = request.query_params.get('period', 'thisMonth')
        comparison = request.query_params.get('comparison', 'lastMonth')

        # Mock P&L data - in production, this would calculate from actual transactions
        report_data = {
            'company_name': 'EMS Company',
            'report_period': 'December 2024',
            'comparison_period': 'November 2024',
            'currency': 'SAR',
            'revenue': [
                {
                    'account_code': '4000',
                    'account_name': 'Sales Revenue',
                    'current_period': 250000,
                    'previous_period': 220000,
                    'variance': 30000,
                    'variance_percent': 13.6,
                    'account_type': 'REVENUE',
                    'level': 1
                },
                {
                    'account_code': '4100',
                    'account_name': 'Service Revenue',
                    'current_period': 75000,
                    'previous_period': 65000,
                    'variance': 10000,
                    'variance_percent': 15.4,
                    'account_type': 'REVENUE',
                    'level': 1
                }
            ],
            'cost_of_goods_sold': [
                {
                    'account_code': '5000',
                    'account_name': 'Cost of Goods Sold',
                    'current_period': 162500,
                    'previous_period': 142500,
                    'variance': 20000,
                    'variance_percent': 14.0,
                    'account_type': 'COGS',
                    'level': 1
                }
            ],
            'gross_profit': {
                'account_code': '',
                'account_name': 'Gross Profit',
                'current_period': 162500,
                'previous_period': 142500,
                'variance': 20000,
                'variance_percent': 14.0,
                'account_type': 'REVENUE',
                'is_subtotal': True,
                'level': 0
            },
            'operating_expenses': [
                {
                    'account_code': '6000',
                    'account_name': 'Salaries & Wages',
                    'current_period': 85000,
                    'previous_period': 80000,
                    'variance': 5000,
                    'variance_percent': 6.25,
                    'account_type': 'EXPENSE',
                    'level': 1
                },
                {
                    'account_code': '6100',
                    'account_name': 'Rent Expense',
                    'current_period': 15000,
                    'previous_period': 15000,
                    'variance': 0,
                    'variance_percent': 0,
                    'account_type': 'EXPENSE',
                    'level': 1
                }
            ],
            'operating_income': {
                'account_code': '',
                'account_name': 'Operating Income',
                'current_period': 62500,
                'previous_period': 47500,
                'variance': 15000,
                'variance_percent': 31.6,
                'account_type': 'REVENUE',
                'is_subtotal': True,
                'level': 0
            },
            'other_income': [],
            'other_expenses': [],
            'net_income_before_tax': {
                'account_code': '',
                'account_name': 'Net Income Before Tax',
                'current_period': 62500,
                'previous_period': 47500,
                'variance': 15000,
                'variance_percent': 31.6,
                'account_type': 'REVENUE',
                'is_subtotal': True,
                'level': 0
            },
            'tax_expense': {
                'account_code': '7000',
                'account_name': 'Income Tax Expense',
                'current_period': 12500,
                'previous_period': 9500,
                'variance': 3000,
                'variance_percent': 31.6,
                'account_type': 'EXPENSE',
                'level': 1
            },
            'net_income': {
                'account_code': '',
                'account_name': 'Net Income',
                'current_period': 50000,
                'previous_period': 38000,
                'variance': 12000,
                'variance_percent': 31.6,
                'account_type': 'REVENUE',
                'is_total': True,
                'level': 0
            },
            'generated_at': timezone.now().isoformat()
        }

        return Response(report_data)


class CurrencyViewSet(viewsets.ModelViewSet):
    """
    Currency management API
    """
    queryset = Currency.objects.all()
    serializer_class = CurrencySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'is_base_currency']
    search_fields = ['code', 'name']
    ordering_fields = ['code', 'name', 'created_at']
    ordering = ['code']

    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get all active currencies"""
        active_currencies = self.get_queryset().filter(is_active=True)
        serializer = self.get_serializer(active_currencies, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def base_currency(self, request):
        """Get the base currency"""
        try:
            base_currency = Currency.objects.get(is_base_currency=True)
            serializer = self.get_serializer(base_currency)
            return Response(serializer.data)
        except Currency.DoesNotExist:
            return Response({'error': 'No base currency configured'}, status=404)

    @action(detail=True, methods=['post'])
    def set_as_base(self, request, pk=None):
        """Set this currency as the base currency"""
        currency = self.get_object()

        # Update all currencies to not be base
        Currency.objects.all().update(is_base_currency=False)

        # Set this currency as base
        currency.is_base_currency = True
        currency.save()

        return Response({'message': f'{currency.code} set as base currency'})


class ExchangeRateViewSet(viewsets.ModelViewSet):
    """
    Exchange rate management API
    """
    queryset = ExchangeRate.objects.all()
    serializer_class = ExchangeRateSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['from_currency', 'to_currency', 'source', 'is_active']
    search_fields = ['from_currency__code', 'to_currency__code']
    ordering_fields = ['effective_date', 'rate']
    ordering = ['-effective_date']

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=False, methods=['get'])
    def current_rates(self, request):
        """Get current exchange rates for all currency pairs"""
        from django.utils import timezone

        today = timezone.now().date()

        # Get all active currencies
        currencies = Currency.objects.filter(is_active=True)

        rates_data = []
        for from_currency in currencies:
            for to_currency in currencies:
                if from_currency != to_currency:
                    try:
                        rate = ExchangeRate.get_rate(from_currency, to_currency, today)
                        rates_data.append({
                            'from_currency': from_currency.code,
                            'to_currency': to_currency.code,
                            'rate': float(rate),
                            'date': today.isoformat()
                        })
                    except ValueError:
                        # No rate available
                        pass

        return Response(rates_data)

    @action(detail=False, methods=['post'])
    def bulk_update(self, request):
        """Bulk update exchange rates"""
        rates_data = request.data.get('rates', [])

        if not rates_data:
            return Response({'error': 'No rates data provided'}, status=400)

        created_rates = []
        errors = []

        for rate_data in rates_data:
            try:
                from_currency = Currency.objects.get(code=rate_data['from_currency'])
                to_currency = Currency.objects.get(code=rate_data['to_currency'])

                rate, created = ExchangeRate.objects.update_or_create(
                    from_currency=from_currency,
                    to_currency=to_currency,
                    effective_date=rate_data['effective_date'],
                    defaults={
                        'rate': rate_data['rate'],
                        'source': rate_data.get('source', 'manual'),
                        'is_active': True,
                        'created_by': Employee.objects.get(user=self.request.user)
                    }
                )

                created_rates.append({
                    'from_currency': from_currency.code,
                    'to_currency': to_currency.code,
                    'rate': float(rate.rate),
                    'created': created
                })

            except (Currency.DoesNotExist, KeyError, ValueError) as e:
                errors.append({
                    'data': rate_data,
                    'error': str(e)
                })

        return Response({
            'created_rates': created_rates,
            'errors': errors,
            'success_count': len(created_rates),
            'error_count': len(errors)
        })

    @action(detail=False, methods=['get'])
    def get_rate(self, request):
        """Get exchange rate between two currencies for a specific date"""
        from_currency_code = request.query_params.get('from_currency')
        to_currency_code = request.query_params.get('to_currency')
        date_str = request.query_params.get('date')

        if not all([from_currency_code, to_currency_code]):
            return Response({'error': 'from_currency and to_currency are required'}, status=400)

        try:
            from_currency = Currency.objects.get(code=from_currency_code)
            to_currency = Currency.objects.get(code=to_currency_code)

            if date_str:
                from datetime import datetime
                date = datetime.strptime(date_str, '%Y-%m-%d').date()
            else:
                from django.utils import timezone
                date = timezone.now().date()

            rate = ExchangeRate.get_rate(from_currency, to_currency, date)

            return Response({
                'from_currency': from_currency_code,
                'to_currency': to_currency_code,
                'rate': float(rate),
                'date': date.isoformat()
            })

        except Currency.DoesNotExist:
            return Response({'error': 'Currency not found'}, status=404)
        except ValueError as e:
            return Response({'error': str(e)}, status=404)


class AssetCategoryViewSet(viewsets.ModelViewSet):
    """
    Asset Category management API
    """
    queryset = AssetCategory.objects.all()
    serializer_class = AssetCategorySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'name_ar', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']


class AssetViewSet(viewsets.ModelViewSet):
    """
    Enhanced Asset management API with depreciation and maintenance tracking
    """
    queryset = Asset.objects.all()
    serializer_class = AssetSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'category', 'status', 'condition', 'assigned_to', 'department',
        'depreciation_method', 'is_active'
    ]
    search_fields = [
        'asset_id', 'name', 'name_ar', 'serial_number', 'model', 'manufacturer',
        'barcode', 'location', 'tags'
    ]
    ordering_fields = [
        'asset_id', 'name', 'purchase_date', 'purchase_price', 'current_value',
        'created_at', 'updated_at'
    ]
    ordering = ['-created_at']

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Get asset dashboard statistics"""
        queryset = self.get_queryset()

        # Basic counts
        total_assets = queryset.count()
        active_assets = queryset.filter(is_active=True).count()

        # Status breakdown
        status_counts = queryset.values('status').annotate(count=Count('id'))

        # Condition breakdown
        condition_counts = queryset.values('condition').annotate(count=Count('id'))

        # Financial summary
        total_value = queryset.aggregate(
            purchase_value=Sum('purchase_price'),
            current_value=Sum('current_value')
        )

        # Depreciation summary
        total_depreciation = sum(asset.get_accumulated_depreciation() for asset in queryset)

        # Maintenance alerts
        maintenance_due = queryset.filter(
            next_maintenance_date__lte=timezone.now().date()
        ).count()

        # Warranty alerts
        warranty_expiring = queryset.filter(
            warranty_expiry__lte=timezone.now().date() + timedelta(days=30),
            warranty_expiry__gte=timezone.now().date()
        ).count()

        return Response({
            'total_assets': total_assets,
            'active_assets': active_assets,
            'status_breakdown': list(status_counts),
            'condition_breakdown': list(condition_counts),
            'financial_summary': {
                'total_purchase_value': total_value['purchase_value'] or 0,
                'total_current_value': total_value['current_value'] or 0,
                'total_depreciation': total_depreciation,
            },
            'alerts': {
                'maintenance_due': maintenance_due,
                'warranty_expiring': warranty_expiring,
            }
        })

    @action(detail=False, methods=['get'])
    def depreciation_report(self, request):
        """Generate depreciation report for all assets"""
        year = request.query_params.get('year', timezone.now().year)

        assets = self.get_queryset().filter(is_active=True)
        report_data = []

        for asset in assets:
            if asset.depreciation_method != 'NO_DEPRECIATION':
                report_data.append({
                    'asset_id': asset.asset_id,
                    'asset_name': asset.name,
                    'category': asset.category.name,
                    'purchase_date': asset.purchase_date,
                    'purchase_price': float(asset.purchase_price),
                    'depreciation_method': asset.depreciation_method,
                    'useful_life_years': asset.useful_life_years,
                    'current_book_value': float(asset.get_current_book_value()),
                    'accumulated_depreciation': float(asset.get_accumulated_depreciation()),
                    'annual_depreciation': float(asset.get_annual_depreciation(year)),
                })

        return Response({
            'year': year,
            'total_assets': len(report_data),
            'total_book_value': sum(item['current_book_value'] for item in report_data),
            'total_accumulated_depreciation': sum(item['accumulated_depreciation'] for item in report_data),
            'total_annual_depreciation': sum(item['annual_depreciation'] for item in report_data),
            'assets': report_data
        })

    @action(detail=True, methods=['post'])
    def calculate_depreciation(self, request, pk=None):
        """Calculate and record depreciation for a specific asset"""
        asset = self.get_object()
        period_start = request.data.get('period_start')
        period_end = request.data.get('period_end')

        if not period_start or not period_end:
            return Response({'error': 'period_start and period_end are required'}, status=400)

        try:
            from datetime import datetime
            start_date = datetime.strptime(period_start, '%Y-%m-%d').date()
            end_date = datetime.strptime(period_end, '%Y-%m-%d').date()

            # Calculate depreciation for the period
            annual_depreciation = asset.get_annual_depreciation()
            days_in_period = (end_date - start_date).days
            period_depreciation = (annual_depreciation * days_in_period) / 365.25

            # Get accumulated depreciation up to period end
            accumulated_depreciation = asset.get_accumulated_depreciation()
            book_value = asset.get_current_book_value()

            # Create depreciation record
            employee = Employee.objects.get(user=request.user)
            depreciation_record = AssetDepreciation.objects.create(
                asset=asset,
                period_start=start_date,
                period_end=end_date,
                depreciation_amount=period_depreciation,
                accumulated_depreciation=accumulated_depreciation,
                book_value=book_value,
                method_used=asset.depreciation_method,
                calculated_by=employee
            )

            serializer = AssetDepreciationSerializer(depreciation_record)
            return Response(serializer.data)

        except ValueError as e:
            return Response({'error': f'Invalid date format: {e}'}, status=400)
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    @action(detail=True, methods=['post'])
    def transfer(self, request, pk=None):
        """Initiate asset transfer"""
        asset = self.get_object()

        transfer_data = request.data.copy()
        transfer_data['asset'] = asset.id

        employee = Employee.objects.get(user=request.user)
        transfer_data['requested_by'] = employee.id

        serializer = AssetTransferSerializer(data=transfer_data)
        if serializer.is_valid():
            transfer = serializer.save()
            return Response(AssetTransferSerializer(transfer).data, status=201)
        return Response(serializer.errors, status=400)

    @action(detail=False, methods=['get'])
    def maintenance_due(self, request):
        """Get assets with maintenance due"""
        days_ahead = int(request.query_params.get('days', 30))
        cutoff_date = timezone.now().date() + timedelta(days=days_ahead)

        assets = self.get_queryset().filter(
            next_maintenance_date__lte=cutoff_date,
            is_active=True
        ).order_by('next_maintenance_date')

        serializer = self.get_serializer(assets, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def warranty_expiring(self, request):
        """Get assets with warranty expiring soon"""
        days_ahead = int(request.query_params.get('days', 30))
        cutoff_date = timezone.now().date() + timedelta(days=days_ahead)

        assets = self.get_queryset().filter(
            warranty_expiry__lte=cutoff_date,
            warranty_expiry__gte=timezone.now().date(),
            is_active=True
        ).order_by('warranty_expiry')

        serializer = self.get_serializer(assets, many=True)
        return Response(serializer.data)


class AssetDepreciationViewSet(viewsets.ModelViewSet):
    """
    Asset Depreciation tracking API
    """
    queryset = AssetDepreciation.objects.all()
    serializer_class = AssetDepreciationSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['asset', 'method_used', 'is_adjustment']
    search_fields = ['asset__asset_id', 'asset__name']
    ordering_fields = ['period_start', 'period_end', 'calculated_at']
    ordering = ['-period_end']

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(calculated_by=employee)


class AssetMaintenanceViewSet(viewsets.ModelViewSet):
    """
    Asset Maintenance management API
    """
    queryset = AssetMaintenance.objects.all()
    serializer_class = AssetMaintenanceSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'asset', 'maintenance_type', 'status', 'priority', 'performed_by'
    ]
    search_fields = ['asset__asset_id', 'asset__name', 'title', 'description']
    ordering_fields = ['scheduled_date', 'created_at', 'priority']
    ordering = ['-scheduled_date']

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """Get overdue maintenance tasks"""
        overdue_maintenance = self.get_queryset().filter(
            scheduled_date__lt=timezone.now().date(),
            status__in=['SCHEDULED', 'IN_PROGRESS']
        )

        serializer = self.get_serializer(overdue_maintenance, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """Get upcoming maintenance tasks"""
        days_ahead = int(request.query_params.get('days', 7))
        cutoff_date = timezone.now().date() + timedelta(days=days_ahead)

        upcoming_maintenance = self.get_queryset().filter(
            scheduled_date__lte=cutoff_date,
            scheduled_date__gte=timezone.now().date(),
            status='SCHEDULED'
        )

        serializer = self.get_serializer(upcoming_maintenance, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """Mark maintenance as completed"""
        maintenance = self.get_object()

        if maintenance.status != 'IN_PROGRESS':
            return Response({'error': 'Maintenance must be in progress to complete'}, status=400)

        # Update maintenance record
        maintenance.status = 'COMPLETED'
        maintenance.actual_end_date = timezone.now()
        maintenance.work_performed = request.data.get('work_performed', '')
        maintenance.parts_used = request.data.get('parts_used', '')
        maintenance.issues_found = request.data.get('issues_found', '')
        maintenance.recommendations = request.data.get('recommendations', '')
        maintenance.actual_cost = request.data.get('actual_cost')

        # Update asset's next maintenance date if provided
        next_maintenance_date = request.data.get('next_maintenance_date')
        if next_maintenance_date:
            maintenance.next_maintenance_date = next_maintenance_date
            maintenance.asset.next_maintenance_date = next_maintenance_date
            maintenance.asset.last_maintenance_date = timezone.now().date()
            maintenance.asset.save()

        maintenance.save()

        serializer = self.get_serializer(maintenance)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get maintenance statistics"""
        queryset = self.get_queryset()

        # Status breakdown
        status_counts = queryset.values('status').annotate(count=Count('id'))

        # Type breakdown
        type_counts = queryset.values('maintenance_type').annotate(count=Count('id'))

        # Cost analysis
        cost_stats = queryset.aggregate(
            total_estimated=Sum('estimated_cost'),
            total_actual=Sum('actual_cost'),
            avg_estimated=Avg('estimated_cost'),
            avg_actual=Avg('actual_cost')
        )

        # Overdue count
        overdue_count = queryset.filter(
            scheduled_date__lt=timezone.now().date(),
            status__in=['SCHEDULED', 'IN_PROGRESS']
        ).count()

        return Response({
            'total_maintenance': queryset.count(),
            'overdue_count': overdue_count,
            'status_breakdown': list(status_counts),
            'type_breakdown': list(type_counts),
            'cost_statistics': cost_stats
        })


class AssetTransferViewSet(viewsets.ModelViewSet):
    """
    Asset Transfer management API
    """
    queryset = AssetTransfer.objects.all()
    serializer_class = AssetTransferSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'asset', 'transfer_type', 'status', 'from_employee', 'to_employee',
        'from_department', 'to_department'
    ]
    search_fields = ['asset__asset_id', 'asset__name', 'reason']
    ordering_fields = ['transfer_date', 'created_at']
    ordering = ['-transfer_date']

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(requested_by=employee)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve asset transfer"""
        transfer = self.get_object()

        if transfer.status != 'PENDING':
            return Response({'error': 'Transfer must be pending to approve'}, status=400)

        employee = Employee.objects.get(user=request.user)
        transfer.status = 'APPROVED'
        transfer.approved_by = employee
        transfer.approval_date = timezone.now()
        transfer.save()

        # Update asset assignment
        asset = transfer.asset
        if transfer.to_employee:
            asset.assigned_to = transfer.to_employee
        if transfer.to_department:
            asset.department = transfer.to_department
        if transfer.to_location:
            asset.location = transfer.to_location

        asset.save()

        serializer = self.get_serializer(transfer)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject asset transfer"""
        transfer = self.get_object()

        if transfer.status != 'PENDING':
            return Response({'error': 'Transfer must be pending to reject'}, status=400)

        employee = Employee.objects.get(user=request.user)
        transfer.status = 'REJECTED'
        transfer.approved_by = employee
        transfer.approval_date = timezone.now()
        transfer.rejection_reason = request.data.get('rejection_reason', '')
        transfer.save()

        serializer = self.get_serializer(transfer)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def pending_approvals(self, request):
        """Get transfers pending approval"""
        pending_transfers = self.get_queryset().filter(status='PENDING')
        serializer = self.get_serializer(pending_transfers, many=True)
        return Response(serializer.data)


class AssetAuditViewSet(viewsets.ModelViewSet):
    """
    Asset Audit management API
    """
    queryset = AssetAudit.objects.all()
    serializer_class = AssetAuditSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['asset', 'audit_type', 'status', 'result', 'audited_by']
    search_fields = ['asset__asset_id', 'asset__name', 'discrepancy_notes']
    ordering_fields = ['audit_date', 'created_at']
    ordering = ['-audit_date']

    @action(detail=False, methods=['post'])
    def bulk_create_audit(self, request):
        """Create audit records for multiple assets"""
        asset_ids = request.data.get('asset_ids', [])
        audit_type = request.data.get('audit_type', 'PHYSICAL')
        audit_date = request.data.get('audit_date', timezone.now().date())

        if not asset_ids:
            return Response({'error': 'asset_ids is required'}, status=400)

        employee = Employee.objects.get(user=request.user)
        created_audits = []

        for asset_id in asset_ids:
            try:
                asset = Asset.objects.get(id=asset_id)
                audit = AssetAudit.objects.create(
                    asset=asset,
                    audit_type=audit_type,
                    audit_date=audit_date,
                    expected_location=asset.location,
                    expected_assignee=asset.assigned_to,
                    expected_condition=asset.condition,
                    audited_by=employee
                )
                created_audits.append(audit)
            except Asset.DoesNotExist:
                continue

        serializer = self.get_serializer(created_audits, many=True)
        return Response({
            'created_count': len(created_audits),
            'audits': serializer.data
        })

    @action(detail=False, methods=['get'])
    def discrepancy_report(self, request):
        """Generate audit discrepancy report"""
        discrepancies = self.get_queryset().exclude(result='FOUND')

        # Group by result type
        result_counts = discrepancies.values('result').annotate(count=Count('id'))

        # Recent discrepancies
        recent_discrepancies = discrepancies.filter(
            audit_date__gte=timezone.now().date() - timedelta(days=30)
        )

        serializer = self.get_serializer(recent_discrepancies, many=True)

        return Response({
            'total_discrepancies': discrepancies.count(),
            'recent_discrepancies': recent_discrepancies.count(),
            'result_breakdown': list(result_counts),
            'recent_audits': serializer.data
        })


class KPIMetricViewSet(viewsets.ModelViewSet):
    """
    KPI Metric management API
    """
    queryset = KPIMetric.objects.all()
    serializer_class = KPIMetricSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['metric_type', 'is_active', 'frequency']
    search_fields = ['name', 'name_ar', 'description']
    ordering_fields = ['name', 'metric_type', 'created_at']
    ordering = ['metric_type', 'name']

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=False, methods=['get'])
    def dashboard_metrics(self, request):
        """Get key metrics for dashboard display"""
        metrics = self.get_queryset().filter(is_active=True)

        dashboard_data = []
        for metric in metrics:
            latest_value = metric.values.first()
            if latest_value:
                dashboard_data.append({
                    'id': metric.id,
                    'name': metric.name,
                    'name_ar': metric.name_ar,
                    'type': metric.metric_type,
                    'value': float(latest_value.value),
                    'unit': metric.unit,
                    'status': latest_value.get_status(),
                    'target': float(metric.target_value) if metric.target_value else None,
                    'period_end': latest_value.period_end.isoformat(),
                })

        return Response(dashboard_data)

    @action(detail=True, methods=['get'])
    def trend_data(self, request, pk=None):
        """Get trend data for a specific KPI"""
        metric = self.get_object()
        periods = int(request.query_params.get('periods', 12))

        values = metric.values.all()[:periods]
        trend_data = []

        for value in reversed(values):
            trend_data.append({
                'period_start': value.period_start.isoformat(),
                'period_end': value.period_end.isoformat(),
                'value': float(value.value),
                'status': value.get_status(),
            })

        return Response({
            'metric': {
                'name': metric.name,
                'unit': metric.unit,
                'target': float(metric.target_value) if metric.target_value else None,
            },
            'trend_data': trend_data
        })

    @action(detail=False, methods=['post'])
    def bulk_calculate(self, request):
        """Calculate KPI values for multiple metrics"""
        metric_ids = request.data.get('metric_ids', [])
        period_start = request.data.get('period_start')
        period_end = request.data.get('period_end')

        if not all([metric_ids, period_start, period_end]):
            return Response({'error': 'metric_ids, period_start, and period_end are required'}, status=400)

        from datetime import datetime
        try:
            start_date = datetime.strptime(period_start, '%Y-%m-%d').date()
            end_date = datetime.strptime(period_end, '%Y-%m-%d').date()
        except ValueError:
            return Response({'error': 'Invalid date format. Use YYYY-MM-DD'}, status=400)

        employee = Employee.objects.get(user=self.request.user)
        calculated_values = []

        for metric_id in metric_ids:
            try:
                metric = KPIMetric.objects.get(id=metric_id)
                # Here you would implement the actual KPI calculation logic
                # For now, we'll create a placeholder calculation
                calculated_value = self._calculate_kpi_value(metric, start_date, end_date)

                kpi_value, created = KPIMetricValue.objects.update_or_create(
                    kpi_metric=metric,
                    period_start=start_date,
                    period_end=end_date,
                    defaults={
                        'value': calculated_value,
                        'calculated_by': employee,
                        'data_source': 'bulk_calculation'
                    }
                )

                calculated_values.append({
                    'metric_id': metric.id,
                    'metric_name': metric.name,
                    'value': float(calculated_value),
                    'created': created
                })

            except KPIMetric.DoesNotExist:
                continue

        return Response({
            'calculated_count': len(calculated_values),
            'values': calculated_values
        })

    def _calculate_kpi_value(self, metric, start_date, end_date):
        """Calculate KPI value based on metric configuration"""
        # This is a simplified calculation - in a real implementation,
        # you would have complex logic based on the metric type and formula

        if metric.metric_type == 'FINANCIAL':
            # Example: Calculate revenue growth
            if metric.name.lower().contains('revenue'):
                # Get revenue data from invoices
                from django.db.models import Sum
                revenue = CustomerInvoice.objects.filter(
                    invoice_date__range=[start_date, end_date],
                    status='PAID'
                ).aggregate(total=Sum('total_amount'))['total'] or 0
                return revenue

        elif metric.metric_type == 'ASSET':
            # Example: Asset utilization rate
            if 'utilization' in metric.name.lower():
                total_assets = Asset.objects.filter(is_active=True).count()
                in_use_assets = Asset.objects.filter(is_active=True, status='IN_USE').count()
                return (in_use_assets / total_assets * 100) if total_assets > 0 else 0

        # Default calculation
        return 100.0


class KPIMetricValueViewSet(viewsets.ReadOnlyModelViewSet):
    """
    DEPRECATED: KPI Metric Value tracking API - Read-only access only.
    Manual KPI metric value entry has been disabled to enforce enterprise automation.
    All KPI metric values must be calculated automatically from operational data.
    """
    queryset = KPIMetricValue.objects.all()
    serializer_class = KPIMetricValueSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['kpi_metric', 'is_manual']
    search_fields = ['kpi_metric__name', 'notes']
    ordering_fields = ['period_start', 'period_end', 'calculated_at']
    ordering = ['-period_end']


class ReportTemplateViewSet(viewsets.ModelViewSet):
    """
    Report Template management API
    """
    queryset = ReportTemplate.objects.all()
    serializer_class = ReportTemplateSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['report_type', 'is_active', 'is_public']
    search_fields = ['name', 'name_ar', 'description']
    ordering_fields = ['name', 'report_type', 'created_at']
    ordering = ['report_type', 'name']

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=True, methods=['post'])
    def execute(self, request, pk=None):
        """Execute a report template"""
        template = self.get_object()

        parameters = request.data.get('parameters', {})
        output_format = request.data.get('output_format', 'PDF')

        if output_format not in template.output_formats:
            return Response({'error': f'Output format {output_format} not supported'}, status=400)

        employee = Employee.objects.get(user=request.user)

        # Create execution record
        execution = ReportExecution.objects.create(
            template=template,
            parameters=parameters,
            output_format=output_format,
            requested_by=employee,
            status='PENDING'
        )

        # In a real implementation, you would queue this for background processing
        # For now, we'll return the execution ID

        return Response({
            'execution_id': execution.id,
            'status': 'PENDING',
            'message': 'Report execution started'
        })

    @action(detail=False, methods=['get'])
    def public_templates(self, request):
        """Get public report templates"""
        public_templates = self.get_queryset().filter(is_public=True, is_active=True)
        serializer = self.get_serializer(public_templates, many=True)
        return Response(serializer.data)


class ReportExecutionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Report Execution tracking API
    """
    queryset = ReportExecution.objects.all()
    serializer_class = ReportExecutionSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['template', 'status', 'output_format', 'requested_by']
    search_fields = ['template__name']
    ordering_fields = ['created_at', 'started_at', 'completed_at']
    ordering = ['-created_at']

    def get_queryset(self):
        # Users can only see their own executions unless they're admin
        user = self.request.user
        if user.is_superuser:
            return ReportExecution.objects.all()

        try:
            employee = Employee.objects.get(user=user)
            return ReportExecution.objects.filter(requested_by=employee)
        except Employee.DoesNotExist:
            return ReportExecution.objects.none()

    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download report file"""
        execution = self.get_object()

        if execution.status != 'COMPLETED' or not execution.output_file:
            return Response({'error': 'Report not ready for download'}, status=400)

        # In a real implementation, you would serve the file
        return Response({
            'download_url': execution.output_file.url,
            'file_size': execution.file_size,
            'filename': execution.output_file.name
        })


class DashboardViewSet(viewsets.ModelViewSet):
    """
    Dashboard management API
    """
    queryset = Dashboard.objects.all()
    serializer_class = DashboardSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['dashboard_type', 'is_active', 'is_public', 'department']
    search_fields = ['name', 'name_ar', 'description']
    ordering_fields = ['name', 'dashboard_type', 'created_at']
    ordering = ['dashboard_type', 'name']

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=False, methods=['get'])
    def my_dashboards(self, request):
        """Get dashboards accessible to current user"""
        user = request.user

        try:
            employee = Employee.objects.get(user=user)
            user_roles = employee.roles.values_list('role__name', flat=True)

            # Get public dashboards and role-based dashboards
            accessible_dashboards = self.get_queryset().filter(
                models.Q(is_public=True) |
                models.Q(allowed_roles__overlap=list(user_roles)) |
                models.Q(created_by=employee)
            ).distinct()

            serializer = self.get_serializer(accessible_dashboards, many=True)
            return Response(serializer.data)

        except Employee.DoesNotExist:
            return Response([])

    @action(detail=True, methods=['get'])
    def data(self, request, pk=None):
        """Get dashboard data with widget values"""
        dashboard = self.get_object()

        dashboard_data = {
            'id': dashboard.id,
            'name': dashboard.name,
            'name_ar': dashboard.name_ar,
            'dashboard_type': dashboard.dashboard_type,
            'layout_config': dashboard.layout_config,
            'refresh_interval': dashboard.refresh_interval,
            'widgets': []
        }

        # Process each widget and get its data
        for widget_config in dashboard.widgets:
            widget_data = self._get_widget_data(widget_config)
            dashboard_data['widgets'].append(widget_data)

        return Response(dashboard_data)

    def _get_widget_data(self, widget_config):
        """Get data for a specific widget"""
        widget_type = widget_config.get('type')
        widget_data = widget_config.copy()

        if widget_type == 'kpi_metric':
            # Get KPI metric data
            metric_id = widget_config.get('metric_id')
            if metric_id:
                try:
                    metric = KPIMetric.objects.get(id=metric_id)
                    latest_value = metric.values.first()
                    if latest_value:
                        widget_data['current_value'] = float(latest_value.value)
                        widget_data['status'] = latest_value.get_status()
                        widget_data['unit'] = metric.unit
                        widget_data['target'] = float(metric.target_value) if metric.target_value else None
                except KPIMetric.DoesNotExist:
                    pass

        elif widget_type == 'chart':
            # Get chart data based on configuration
            chart_type = widget_config.get('chart_type')
            data_source = widget_config.get('data_source')

            if data_source == 'financial_summary':
                widget_data['chart_data'] = self._get_financial_summary_data()
            elif data_source == 'asset_status':
                widget_data['chart_data'] = self._get_asset_status_data()

        elif widget_type == 'table':
            # Get table data
            data_source = widget_config.get('data_source')
            if data_source == 'recent_invoices':
                widget_data['table_data'] = self._get_recent_invoices_data()

        return widget_data

    def _get_financial_summary_data(self):
        """Get financial summary data for charts"""
        from django.db.models import Sum
        from datetime import datetime, timedelta

        # Get last 6 months of revenue data
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=180)

        monthly_revenue = []
        current_date = start_date

        while current_date <= end_date:
            month_start = current_date.replace(day=1)
            if current_date.month == 12:
                month_end = current_date.replace(year=current_date.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                month_end = current_date.replace(month=current_date.month + 1, day=1) - timedelta(days=1)

            revenue = CustomerInvoice.objects.filter(
                invoice_date__range=[month_start, month_end],
                status='PAID'
            ).aggregate(total=Sum('total_amount'))['total'] or 0

            monthly_revenue.append({
                'period': month_start.strftime('%Y-%m'),
                'value': float(revenue)
            })

            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)

        return monthly_revenue

    def _get_asset_status_data(self):
        """Get asset status distribution data"""
        from django.db.models import Count

        status_data = Asset.objects.values('status').annotate(
            count=Count('id')
        ).order_by('status')

        return [
            {
                'label': item['status'],
                'value': item['count']
            }
            for item in status_data
        ]

    def _get_recent_invoices_data(self):
        """Get recent invoices data for table widgets"""
        recent_invoices = CustomerInvoice.objects.select_related(
            'customer'
        ).order_by('-created_at')[:10]

        return [
            {
                'invoice_number': invoice.invoice_number,
                'customer': invoice.customer.name,
                'amount': float(invoice.total_amount),
                'status': invoice.status,
                'date': invoice.invoice_date.isoformat()
            }
            for invoice in recent_invoices
        ]


class AnalyticsQueryViewSet(viewsets.ModelViewSet):
    """
    Analytics Query management API
    """
    queryset = AnalyticsQuery.objects.all()
    serializer_class = AnalyticsQuerySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['query_type', 'is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'last_executed', 'execution_count']
    ordering = ['name']

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=True, methods=['post'])
    def execute(self, request, pk=None):
        """Execute an analytics query"""
        query = self.get_object()
        parameters = request.data.get('parameters', {})

        try:
            # Update execution tracking
            query.execution_count += 1
            query.last_executed = timezone.now()
            query.save()

            # Execute the query based on type
            if query.query_type == 'SQL':
                result = self._execute_sql_query(query, parameters)
            elif query.query_type == 'AGGREGATION':
                result = self._execute_aggregation_query(query, parameters)
            else:
                result = {'error': 'Query type not supported'}

            return Response({
                'query_id': query.id,
                'query_name': query.name,
                'execution_time': timezone.now().isoformat(),
                'result': result
            })

        except Exception as e:
            return Response({'error': str(e)}, status=500)

    def _execute_sql_query(self, query, parameters):
        """Execute SQL query (with safety restrictions)"""
        # In a production environment, you would implement proper SQL injection protection
        # and restrict allowed operations
        return {'message': 'SQL query execution not implemented in demo'}

    def _execute_aggregation_query(self, query, parameters):
        """Execute aggregation query"""
        query_def = query.query_definition

        # Example aggregation query execution
        if query_def.get('model') == 'CustomerInvoice':
            from django.db.models import Sum, Count, Avg

            queryset = CustomerInvoice.objects.all()

            # Apply filters from parameters
            if parameters.get('date_from'):
                queryset = queryset.filter(invoice_date__gte=parameters['date_from'])
            if parameters.get('date_to'):
                queryset = queryset.filter(invoice_date__lte=parameters['date_to'])

            # Apply aggregation
            aggregation_type = query_def.get('aggregation', 'sum')
            field = query_def.get('field', 'total_amount')

            if aggregation_type == 'sum':
                result = queryset.aggregate(result=Sum(field))
            elif aggregation_type == 'count':
                result = queryset.aggregate(result=Count('id'))
            elif aggregation_type == 'avg':
                result = queryset.aggregate(result=Avg(field))
            else:
                result = {'result': 0}

            return {
                'aggregation_type': aggregation_type,
                'field': field,
                'result': float(result['result']) if result['result'] else 0,
                'record_count': queryset.count()
            }

        return {'error': 'Model not supported'}


class APIKeyViewSet(viewsets.ModelViewSet):
    """
    API Key management API
    """
    queryset = APIKey.objects.all()
    serializer_class = APIKeySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['key_type', 'status']
    search_fields = ['name', 'description', 'key_id']
    ordering_fields = ['name', 'created_at', 'last_used_at', 'usage_count']
    ordering = ['-created_at']

    def perform_create(self, serializer):
        import secrets
        import hashlib

        employee = Employee.objects.get(user=self.request.user)

        # Generate unique key ID and secret
        key_id = f"{serializer.validated_data.get('key_prefix', 'ems')}_{secrets.token_urlsafe(16)}"
        key_secret = secrets.token_urlsafe(32)
        key_secret_hash = hashlib.sha256(key_secret.encode()).hexdigest()

        api_key = serializer.save(
            created_by=employee,
            key_id=key_id,
            key_secret=key_secret_hash
        )

        # Return the plain secret only once during creation
        response_data = APIKeySerializer(api_key).data
        response_data['key_secret_plain'] = key_secret

        return Response(response_data, status=201)

    @action(detail=True, methods=['post'])
    def regenerate(self, request, pk=None):
        """Regenerate API key secret"""
        import secrets
        import hashlib

        api_key = self.get_object()

        # Generate new secret
        new_secret = secrets.token_urlsafe(32)
        new_secret_hash = hashlib.sha256(new_secret.encode()).hexdigest()

        api_key.key_secret = new_secret_hash
        api_key.usage_count = 0
        api_key.last_used_at = None
        api_key.save()

        return Response({
            'message': 'API key regenerated successfully',
            'key_secret_plain': new_secret,
            'key_id': api_key.key_id
        })

    @action(detail=True, methods=['post'])
    def revoke(self, request, pk=None):
        """Revoke API key"""
        api_key = self.get_object()
        api_key.status = 'REVOKED'
        api_key.save()

        return Response({'message': 'API key revoked successfully'})

    @action(detail=False, methods=['get'])
    def usage_stats(self, request):
        """Get API key usage statistics"""
        from django.db.models import Sum, Avg, Count

        stats = self.get_queryset().aggregate(
            total_keys=Count('id'),
            active_keys=Count('id', filter=models.Q(status='ACTIVE')),
            total_usage=Sum('usage_count'),
            avg_usage=Avg('usage_count')
        )

        # Recent usage (last 30 days)
        from datetime import timedelta
        recent_cutoff = timezone.now() - timedelta(days=30)
        recent_usage = self.get_queryset().filter(
            last_used_at__gte=recent_cutoff
        ).count()

        return Response({
            'total_keys': stats['total_keys'],
            'active_keys': stats['active_keys'],
            'total_usage': stats['total_usage'] or 0,
            'average_usage': round(stats['avg_usage'] or 0, 2),
            'recently_used_keys': recent_usage
        })


class ExternalServiceViewSet(viewsets.ModelViewSet):
    """
    External Service integration management API
    """
    queryset = ExternalService.objects.all()
    serializer_class = ExternalServiceSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['service_type', 'status', 'is_healthy']
    search_fields = ['name', 'description', 'base_url']
    ordering_fields = ['name', 'service_type', 'created_at', 'last_request_at']
    ordering = ['service_type', 'name']

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=True, methods=['post'])
    def health_check(self, request, pk=None):
        """Perform health check on external service"""
        service = self.get_object()

        try:
            import requests

            health_url = service.health_check_url or f"{service.base_url}/health"

            start_time = timezone.now()
            response = requests.get(
                health_url,
                headers=service.headers,
                timeout=service.timeout
            )
            end_time = timezone.now()

            duration_ms = int((end_time - start_time).total_seconds() * 1000)

            is_healthy = response.status_code == 200
            service.is_healthy = is_healthy
            service.last_health_check = timezone.now()
            service.save()

            # Log the health check
            IntegrationLog.objects.create(
                log_type='API_REQUEST',
                severity='INFO',
                external_service=service,
                message=f'Health check for {service.name}',
                details={
                    'url': health_url,
                    'status_code': response.status_code,
                    'response_time_ms': duration_ms
                },
                duration_ms=duration_ms
            )

            return Response({
                'is_healthy': is_healthy,
                'status_code': response.status_code,
                'response_time_ms': duration_ms,
                'checked_at': service.last_health_check.isoformat()
            })

        except Exception as e:
            service.is_healthy = False
            service.last_health_check = timezone.now()
            service.save()

            # Log the error
            IntegrationLog.objects.create(
                log_type='ERROR',
                severity='ERROR',
                external_service=service,
                message=f'Health check failed for {service.name}',
                details={'error': str(e)}
            )

            return Response({
                'is_healthy': False,
                'error': str(e),
                'checked_at': service.last_health_check.isoformat()
            }, status=500)

    @action(detail=True, methods=['post'])
    def test_connection(self, request, pk=None):
        """Test connection to external service"""
        service = self.get_object()
        test_endpoint = request.data.get('endpoint', '')

        try:
            import requests

            test_url = f"{service.base_url}/{test_endpoint}".rstrip('/')

            start_time = timezone.now()
            response = requests.get(
                test_url,
                headers=service.headers,
                timeout=service.timeout
            )
            end_time = timezone.now()

            duration_ms = int((end_time - start_time).total_seconds() * 1000)

            # Log the test
            IntegrationLog.objects.create(
                log_type='API_REQUEST',
                severity='INFO',
                external_service=service,
                message=f'Connection test for {service.name}',
                details={
                    'url': test_url,
                    'status_code': response.status_code,
                    'response_time_ms': duration_ms
                },
                duration_ms=duration_ms
            )

            return Response({
                'success': True,
                'status_code': response.status_code,
                'response_time_ms': duration_ms,
                'response_size': len(response.content)
            })

        except Exception as e:
            # Log the error
            IntegrationLog.objects.create(
                log_type='ERROR',
                severity='ERROR',
                external_service=service,
                message=f'Connection test failed for {service.name}',
                details={'error': str(e), 'endpoint': test_endpoint}
            )

            return Response({
                'success': False,
                'error': str(e)
            }, status=500)

    @action(detail=False, methods=['get'])
    def service_stats(self, request):
        """Get external service statistics"""
        from django.db.models import Sum, Avg, Count

        stats = self.get_queryset().aggregate(
            total_services=Count('id'),
            active_services=Count('id', filter=models.Q(status='ACTIVE')),
            healthy_services=Count('id', filter=models.Q(is_healthy=True)),
            total_requests=Sum('total_requests'),
            avg_success_rate=Avg('successful_requests') * 100 / Avg('total_requests')
        )

        # Service type breakdown
        type_breakdown = self.get_queryset().values('service_type').annotate(
            count=Count('id'),
            healthy_count=Count('id', filter=models.Q(is_healthy=True))
        ).order_by('service_type')

        return Response({
            'total_services': stats['total_services'],
            'active_services': stats['active_services'],
            'healthy_services': stats['healthy_services'],
            'total_requests': stats['total_requests'] or 0,
            'average_success_rate': round(stats['avg_success_rate'] or 0, 2),
            'service_type_breakdown': list(type_breakdown)
        })


class WebhookEndpointViewSet(viewsets.ModelViewSet):
    """
    Webhook Endpoint management API
    """
    queryset = WebhookEndpoint.objects.all()
    serializer_class = WebhookEndpointSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'is_verified']
    search_fields = ['name', 'description', 'url']
    ordering_fields = ['name', 'created_at', 'last_triggered_at']
    ordering = ['-created_at']

    def perform_create(self, serializer):
        import secrets

        employee = Employee.objects.get(user=self.request.user)

        # Generate webhook secret if not provided
        if not serializer.validated_data.get('secret'):
            webhook_secret = secrets.token_urlsafe(32)
            serializer.save(created_by=employee, secret=webhook_secret)
        else:
            serializer.save(created_by=employee)

    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """Verify webhook endpoint by sending test event"""
        webhook = self.get_object()

        try:
            import requests
            import json
            import uuid

            # Create test event
            test_event = {
                'event_id': str(uuid.uuid4()),
                'event_type': 'WEBHOOK_VERIFICATION',
                'timestamp': timezone.now().isoformat(),
                'data': {
                    'message': 'This is a test webhook verification event',
                    'webhook_id': webhook.id,
                    'webhook_name': webhook.name
                }
            }

            # Prepare headers
            headers = webhook.headers.copy()
            headers['Content-Type'] = 'application/json'
            if webhook.secret:
                import hmac
                import hashlib

                payload_str = json.dumps(test_event)
                signature = hmac.new(
                    webhook.secret.encode(),
                    payload_str.encode(),
                    hashlib.sha256
                ).hexdigest()
                headers['X-Webhook-Signature'] = f'sha256={signature}'

            # Send test webhook
            start_time = timezone.now()
            response = requests.request(
                method=webhook.method,
                url=webhook.url,
                json=test_event,
                headers=headers,
                timeout=webhook.timeout
            )
            end_time = timezone.now()

            duration_ms = int((end_time - start_time).total_seconds() * 1000)

            # Update verification status
            webhook.is_verified = response.status_code == 200
            webhook.save()

            # Log the verification attempt
            IntegrationLog.objects.create(
                log_type='WEBHOOK_SENT',
                severity='INFO',
                webhook_endpoint=webhook,
                message=f'Webhook verification for {webhook.name}',
                details={
                    'status_code': response.status_code,
                    'response_time_ms': duration_ms,
                    'verified': webhook.is_verified
                },
                duration_ms=duration_ms
            )

            return Response({
                'verified': webhook.is_verified,
                'status_code': response.status_code,
                'response_time_ms': duration_ms,
                'response_body': response.text[:500]  # Truncate response
            })

        except Exception as e:
            webhook.is_verified = False
            webhook.save()

            # Log the error
            IntegrationLog.objects.create(
                log_type='ERROR',
                severity='ERROR',
                webhook_endpoint=webhook,
                message=f'Webhook verification failed for {webhook.name}',
                details={'error': str(e)}
            )

            return Response({
                'verified': False,
                'error': str(e)
            }, status=500)

    @action(detail=True, methods=['post'])
    def trigger_event(self, request, pk=None):
        """Manually trigger a webhook event"""
        webhook = self.get_object()

        event_type = request.data.get('event_type', 'CUSTOM')
        payload = request.data.get('payload', {})

        if event_type not in webhook.event_types and 'CUSTOM' not in webhook.event_types:
            return Response({
                'error': f'Event type {event_type} not supported by this webhook'
            }, status=400)

        try:
            import uuid

            # Create webhook event
            event_id = str(uuid.uuid4())
            webhook_event = WebhookEvent.objects.create(
                webhook_endpoint=webhook,
                event_type=event_type,
                event_id=event_id,
                payload=payload,
                headers=webhook.headers
            )

            # Queue for delivery (in a real implementation, this would be handled by a task queue)
            # For now, we'll attempt immediate delivery
            success = self._deliver_webhook_event(webhook_event)

            return Response({
                'event_id': event_id,
                'status': 'DELIVERED' if success else 'FAILED',
                'message': 'Event triggered successfully' if success else 'Event delivery failed'
            })

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=500)

    def _deliver_webhook_event(self, webhook_event):
        """Deliver a webhook event (simplified implementation)"""
        try:
            import requests
            import json
            import hmac
            import hashlib

            webhook = webhook_event.webhook_endpoint

            # Prepare headers
            headers = webhook_event.headers.copy()
            headers['Content-Type'] = 'application/json'
            headers['X-Event-ID'] = webhook_event.event_id
            headers['X-Event-Type'] = webhook_event.event_type

            # Add signature if secret is configured
            if webhook.secret:
                payload_str = json.dumps(webhook_event.payload)
                signature = hmac.new(
                    webhook.secret.encode(),
                    payload_str.encode(),
                    hashlib.sha256
                ).hexdigest()
                headers['X-Webhook-Signature'] = f'sha256={signature}'

            # Attempt delivery
            webhook_event.attempts += 1
            webhook_event.first_attempted_at = webhook_event.first_attempted_at or timezone.now()
            webhook_event.last_attempted_at = timezone.now()
            webhook_event.save()

            response = requests.request(
                method=webhook.method,
                url=webhook.url,
                json=webhook_event.payload,
                headers=headers,
                timeout=webhook.timeout
            )

            if response.status_code == 200:
                webhook_event.mark_delivered(response.status_code, response.text[:1000])
                webhook.increment_call_count(success=True)
                return True
            else:
                webhook_event.mark_failed(
                    f'HTTP {response.status_code}',
                    response.status_code,
                    response.text[:1000]
                )
                webhook.increment_call_count(success=False)
                return False

        except Exception as e:
            webhook_event.mark_failed(str(e))
            webhook.increment_call_count(success=False)
            return False

    @action(detail=False, methods=['get'])
    def webhook_stats(self, request):
        """Get webhook statistics"""
        from django.db.models import Sum, Avg, Count

        stats = self.get_queryset().aggregate(
            total_webhooks=Count('id'),
            active_webhooks=Count('id', filter=models.Q(status='ACTIVE')),
            verified_webhooks=Count('id', filter=models.Q(is_verified=True)),
            total_calls=Sum('total_calls'),
            avg_success_rate=Avg('successful_calls') * 100 / Avg('total_calls')
        )

        return Response({
            'total_webhooks': stats['total_webhooks'],
            'active_webhooks': stats['active_webhooks'],
            'verified_webhooks': stats['verified_webhooks'],
            'total_calls': stats['total_calls'] or 0,
            'average_success_rate': round(stats['avg_success_rate'] or 0, 2)
        })


class WebhookEventViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Webhook Event tracking API (read-only)
    """
    queryset = WebhookEvent.objects.all()
    serializer_class = WebhookEventSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['webhook_endpoint', 'event_type', 'status']
    search_fields = ['event_id', 'event_type']
    ordering_fields = ['created_at', 'scheduled_at', 'delivered_at']
    ordering = ['-created_at']

    @action(detail=True, methods=['post'])
    def retry(self, request, pk=None):
        """Retry failed webhook event"""
        event = self.get_object()

        if not event.can_retry():
            return Response({
                'error': 'Event cannot be retried (max attempts reached or not in failed state)'
            }, status=400)

        # Reset status and schedule for retry
        event.status = 'PENDING'
        event.scheduled_at = timezone.now()
        event.save()

        # In a real implementation, this would be queued for background processing
        # For now, attempt immediate delivery
        webhook_endpoint_view = WebhookEndpointViewSet()
        success = webhook_endpoint_view._deliver_webhook_event(event)

        return Response({
            'message': 'Retry attempted',
            'success': success,
            'status': event.status
        })

    @action(detail=False, methods=['get'])
    def event_stats(self, request):
        """Get webhook event statistics"""
        from django.db.models import Count
        from datetime import timedelta

        # Overall stats
        total_events = self.get_queryset().count()
        status_breakdown = self.get_queryset().values('status').annotate(count=Count('id'))

        # Recent events (last 24 hours)
        recent_cutoff = timezone.now() - timedelta(hours=24)
        recent_events = self.get_queryset().filter(created_at__gte=recent_cutoff).count()

        # Failed events that can be retried
        retryable_events = self.get_queryset().filter(
            status__in=['FAILED', 'RETRYING'],
            attempts__lt=models.F('max_attempts')
        ).count()

        return Response({
            'total_events': total_events,
            'recent_events_24h': recent_events,
            'retryable_events': retryable_events,
            'status_breakdown': list(status_breakdown)
        })


class IntegrationLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Integration Log API (read-only)
    """
    queryset = IntegrationLog.objects.all()
    serializer_class = IntegrationLogSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['log_type', 'severity', 'external_service', 'webhook_endpoint']
    search_fields = ['message']
    ordering_fields = ['created_at', 'severity', 'duration_ms']
    ordering = ['-created_at']

    @action(detail=False, methods=['get'])
    def log_stats(self, request):
        """Get integration log statistics"""
        from django.db.models import Count, Avg
        from datetime import timedelta

        # Overall stats
        total_logs = self.get_queryset().count()
        severity_breakdown = self.get_queryset().values('severity').annotate(count=Count('id'))
        type_breakdown = self.get_queryset().values('log_type').annotate(count=Count('id'))

        # Recent logs (last 24 hours)
        recent_cutoff = timezone.now() - timedelta(hours=24)
        recent_logs = self.get_queryset().filter(created_at__gte=recent_cutoff)
        recent_count = recent_logs.count()
        recent_errors = recent_logs.filter(severity__in=['ERROR', 'CRITICAL']).count()

        # Performance stats
        avg_duration = self.get_queryset().filter(
            duration_ms__isnull=False
        ).aggregate(avg_duration=Avg('duration_ms'))

        return Response({
            'total_logs': total_logs,
            'recent_logs_24h': recent_count,
            'recent_errors_24h': recent_errors,
            'average_duration_ms': round(avg_duration['avg_duration'] or 0, 2),
            'severity_breakdown': list(severity_breakdown),
            'type_breakdown': list(type_breakdown)
        })




class UserSecurityProfileViewSet(viewsets.ModelViewSet):
    """
    User Security Profile management API
    """
    queryset = UserSecurityProfile.objects.all()
    serializer_class = UserSecurityProfileSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['security_level', 'mfa_enabled', 'mfa_method']
    search_fields = ['employee__user__username', 'employee__user__first_name', 'employee__user__last_name']
    ordering_fields = ['security_level', 'last_login_ip', 'created_at']
    ordering = ['-security_level', 'employee__user__username']

    @action(detail=True, methods=['post'])
    def enable_mfa(self, request, pk=None):
        """Enable multi-factor authentication for user"""
        profile = self.get_object()
        mfa_method = request.data.get('mfa_method', 'TOTP')

        if mfa_method not in ['SMS', 'EMAIL', 'TOTP', 'HARDWARE']:
            return Response({'error': 'Invalid MFA method'}, status=400)

        # Generate MFA secret for TOTP
        if mfa_method == 'TOTP':
            import pyotp
            secret = pyotp.random_base32()
            profile.mfa_secret = secret  # In production, encrypt this

        profile.mfa_enabled = True
        profile.mfa_method = mfa_method
        profile.save()

        # Log security change
        AuditTrail.objects.create(
            action_type='UPDATE',
            action_description=f'Enabled MFA ({mfa_method}) for user {profile.employee.user.username}',
            risk_level='MEDIUM',
            user=request.user,
            employee=Employee.objects.get(user=request.user),
            resource_type='UserSecurityProfile',
            resource_id=str(profile.id),
            resource_name=profile.employee.user.username,
            new_values={'mfa_enabled': True, 'mfa_method': mfa_method},
            ip_address=request.META.get('REMOTE_ADDR'),
            compliance_category='AUTHENTICATION'
        )

        response_data = {
            'message': 'MFA enabled successfully',
            'mfa_method': mfa_method
        }

        if mfa_method == 'TOTP':
            response_data['qr_code_url'] = f'otpauth://totp/EMS:{profile.employee.user.username}?secret={secret}&issuer=EMS'

        return Response(response_data)

    @action(detail=True, methods=['post'])
    def unlock_account(self, request, pk=None):
        """Unlock a locked user account"""
        profile = self.get_object()

        if not profile.is_account_locked():
            return Response({'message': 'Account is not locked'})

        profile.failed_login_attempts = 0
        profile.account_locked_until = None
        profile.save()

        # Log security action
        AuditTrail.objects.create(
            action_type='UPDATE',
            action_description=f'Unlocked account for user {profile.employee.user.username}',
            risk_level='HIGH',
            user=request.user,
            employee=Employee.objects.get(user=request.user),
            resource_type='UserSecurityProfile',
            resource_id=str(profile.id),
            resource_name=profile.employee.user.username,
            ip_address=request.META.get('REMOTE_ADDR'),
            compliance_category='AUTHENTICATION'
        )

        return Response({'message': 'Account unlocked successfully'})

    @action(detail=False, methods=['get'])
    def security_stats(self, request):
        """Get security statistics"""
        from django.db.models import Count, Q

        stats = self.get_queryset().aggregate(
            total_users=Count('id'),
            mfa_enabled_users=Count('id', filter=Q(mfa_enabled=True)),
            locked_accounts=Count('id', filter=Q(account_locked_until__gt=timezone.now())),
            high_security_users=Count('id', filter=Q(security_level='HIGH')),
            critical_security_users=Count('id', filter=Q(security_level='CRITICAL'))
        )

        # Recent security events
        recent_cutoff = timezone.now() - timedelta(hours=24)
        recent_failed_logins = self.get_queryset().filter(
            failed_login_attempts__gt=0,
            updated_at__gte=recent_cutoff
        ).count()

        return Response({
            'total_users': stats['total_users'],
            'mfa_enabled_users': stats['mfa_enabled_users'],
            'mfa_adoption_rate': round((stats['mfa_enabled_users'] / stats['total_users']) * 100, 2) if stats['total_users'] > 0 else 0,
            'locked_accounts': stats['locked_accounts'],
            'high_security_users': stats['high_security_users'],
            'critical_security_users': stats['critical_security_users'],
            'recent_failed_logins_24h': recent_failed_logins
        })


class AuditTrailViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Audit Trail API (read-only for compliance)
    """
    queryset = AuditTrail.objects.all()
    serializer_class = AuditTrailSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['action_type', 'risk_level', 'user', 'resource_type', 'compliance_category']
    search_fields = ['action_description', 'resource_name', 'user__username']
    ordering_fields = ['timestamp', 'risk_level', 'action_type']
    ordering = ['-timestamp']

    @action(detail=False, methods=['get'])
    def compliance_report(self, request):
        """Generate compliance audit report"""
        from datetime import datetime

        # Get date range from query params
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        queryset = self.get_queryset()

        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            queryset = queryset.filter(timestamp__date__gte=start_date)

        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            queryset = queryset.filter(timestamp__date__lte=end_date)

        # Generate statistics
        stats = queryset.aggregate(
            total_actions=Count('id'),
            high_risk_actions=Count('id', filter=Q(risk_level='HIGH')),
            critical_risk_actions=Count('id', filter=Q(risk_level='CRITICAL')),
            unique_users=Count('user', distinct=True)
        )

        # Action type breakdown
        action_breakdown = queryset.values('action_type').annotate(
            count=Count('id')
        ).order_by('-count')

        # Risk level breakdown
        risk_breakdown = queryset.values('risk_level').annotate(
            count=Count('id')
        ).order_by('risk_level')

        # Compliance category breakdown
        compliance_breakdown = queryset.values('compliance_category').annotate(
            count=Count('id')
        ).order_by('-count')

        return Response({
            'period': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            },
            'statistics': stats,
            'action_breakdown': list(action_breakdown),
            'risk_breakdown': list(risk_breakdown),
            'compliance_breakdown': list(compliance_breakdown)
        })

    @action(detail=False, methods=['get'])
    def user_activity(self, request):
        """Get user activity summary"""
        user_id = request.query_params.get('user_id')
        days = int(request.query_params.get('days', 30))

        if not user_id:
            return Response({'error': 'user_id parameter required'}, status=400)

        cutoff_date = timezone.now() - timedelta(days=days)

        user_actions = self.get_queryset().filter(
            user_id=user_id,
            timestamp__gte=cutoff_date
        )

        activity_summary = user_actions.values('action_type').annotate(
            count=Count('id'),
            last_action=models.Max('timestamp')
        ).order_by('-count')

        risk_summary = user_actions.values('risk_level').annotate(
            count=Count('id')
        ).order_by('risk_level')

        return Response({
            'user_id': user_id,
            'period_days': days,
            'total_actions': user_actions.count(),
            'activity_breakdown': list(activity_summary),
            'risk_breakdown': list(risk_summary)
        })


class SecurityIncidentViewSet(viewsets.ModelViewSet):
    """
    Security Incident management API
    """
    queryset = SecurityIncident.objects.all()
    serializer_class = SecurityIncidentSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['incident_type', 'severity', 'status', 'assigned_to']
    search_fields = ['incident_id', 'title', 'description']
    ordering_fields = ['severity', 'detected_at', 'status']
    ordering = ['-severity', '-detected_at']

    def perform_create(self, serializer):
        import uuid

        # Generate unique incident ID
        incident_id = f"SEC-{timezone.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"

        employee = Employee.objects.get(user=self.request.user)
        serializer.save(
            incident_id=incident_id,
            reported_by=employee
        )

    @action(detail=True, methods=['post'])
    def assign_incident(self, request, pk=None):
        """Assign incident to security team member"""
        incident = self.get_object()
        assignee_id = request.data.get('assignee_id')

        try:
            assignee = Employee.objects.get(id=assignee_id)
            incident.assigned_to = assignee
            incident.save()

            # Log assignment
            AuditTrail.objects.create(
                action_type='UPDATE',
                action_description=f'Assigned incident {incident.incident_id} to {assignee.user.get_full_name()}',
                risk_level='MEDIUM',
                user=request.user,
                employee=Employee.objects.get(user=request.user),
                resource_type='SecurityIncident',
                resource_id=str(incident.id),
                resource_name=incident.incident_id,
                new_values={'assigned_to': assignee.id},
                ip_address=request.META.get('REMOTE_ADDR'),
                compliance_category='INCIDENT_MANAGEMENT'
            )

            return Response({
                'message': f'Incident assigned to {assignee.user.get_full_name()}'
            })

        except Employee.DoesNotExist:
            return Response({'error': 'Invalid assignee'}, status=400)

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """Update incident status with timeline tracking"""
        incident = self.get_object()
        new_status = request.data.get('status')
        notes = request.data.get('notes', '')

        if new_status not in ['OPEN', 'INVESTIGATING', 'CONTAINED', 'RESOLVED', 'CLOSED']:
            return Response({'error': 'Invalid status'}, status=400)

        old_status = incident.status
        incident.status = new_status

        # Update timeline based on status
        if new_status == 'CONTAINED' and not incident.contained_at:
            incident.contained_at = timezone.now()
        elif new_status == 'RESOLVED' and not incident.resolved_at:
            incident.resolved_at = timezone.now()

        incident.save()

        # Log status change
        AuditTrail.objects.create(
            action_type='UPDATE',
            action_description=f'Updated incident {incident.incident_id} status from {old_status} to {new_status}',
            risk_level='MEDIUM',
            user=request.user,
            employee=Employee.objects.get(user=request.user),
            resource_type='SecurityIncident',
            resource_id=str(incident.id),
            resource_name=incident.incident_id,
            old_values={'status': old_status},
            new_values={'status': new_status},
            additional_context={'notes': notes},
            ip_address=request.META.get('REMOTE_ADDR'),
            compliance_category='INCIDENT_MANAGEMENT'
        )

        return Response({
            'message': f'Incident status updated to {new_status}',
            'timeline_updated': incident.contained_at or incident.resolved_at
        })

    @action(detail=False, methods=['get'])
    def incident_stats(self, request):
        """Get security incident statistics"""
        from django.db.models import Avg

        stats = self.get_queryset().aggregate(
            total_incidents=Count('id'),
            open_incidents=Count('id', filter=Q(status__in=['OPEN', 'INVESTIGATING'])),
            critical_incidents=Count('id', filter=Q(severity='CRITICAL')),
            resolved_incidents=Count('id', filter=Q(status='RESOLVED'))
        )

        # Average resolution time for resolved incidents
        resolved_incidents = self.get_queryset().filter(
            status='RESOLVED',
            resolved_at__isnull=False
        )

        if resolved_incidents.exists():
            avg_resolution_hours = resolved_incidents.aggregate(
                avg_hours=Avg(
                    models.ExpressionWrapper(
                        models.F('resolved_at') - models.F('detected_at'),
                        output_field=models.DurationField()
                    )
                )
            )['avg_hours']

            if avg_resolution_hours:
                avg_resolution_hours = avg_resolution_hours.total_seconds() / 3600
        else:
            avg_resolution_hours = 0

        # Recent incidents (last 30 days)
        recent_cutoff = timezone.now() - timedelta(days=30)
        recent_incidents = self.get_queryset().filter(detected_at__gte=recent_cutoff).count()

        # Incident type breakdown
        type_breakdown = self.get_queryset().values('incident_type').annotate(
            count=Count('id')
        ).order_by('-count')

        return Response({
            'total_incidents': stats['total_incidents'],
            'open_incidents': stats['open_incidents'],
            'critical_incidents': stats['critical_incidents'],
            'resolved_incidents': stats['resolved_incidents'],
            'recent_incidents_30d': recent_incidents,
            'average_resolution_hours': round(avg_resolution_hours, 2) if avg_resolution_hours else 0,
            'incident_type_breakdown': list(type_breakdown)
        })


class ComplianceFrameworkViewSet(viewsets.ModelViewSet):
    """
    Compliance Framework management API
    """
    queryset = ComplianceFramework.objects.all()
    serializer_class = ComplianceFrameworkSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['framework_type', 'is_active', 'implementation_status']
    search_fields = ['name', 'name_ar', 'description']
    ordering_fields = ['name', 'framework_type', 'compliance_score', 'created_at']
    ordering = ['framework_type', 'name']

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=True, methods=['post'])
    def update_compliance_score(self, request, pk=None):
        """Update compliance score based on control implementation"""
        framework = self.get_object()

        # Calculate compliance score based on implemented controls
        total_controls = framework.controls.count()
        if total_controls == 0:
            compliance_score = 0
        else:
            implemented_controls = framework.controls.filter(status='IMPLEMENTED').count()
            compliance_score = (implemented_controls / total_controls) * 100

        framework.compliance_score = compliance_score
        framework.save()

        return Response({
            'compliance_score': compliance_score,
            'total_controls': total_controls,
            'implemented_controls': implemented_controls
        })

    @action(detail=True, methods=['post'])
    def schedule_assessment(self, request, pk=None):
        """Schedule compliance assessment"""
        framework = self.get_object()
        assessment_date = request.data.get('assessment_date')

        if not assessment_date:
            return Response({'error': 'assessment_date required'}, status=400)

        try:
            from datetime import datetime
            assessment_date = datetime.strptime(assessment_date, '%Y-%m-%d').date()

            framework.next_assessment_due = assessment_date
            framework.save()

            # Log assessment scheduling
            AuditTrail.objects.create(
                action_type='UPDATE',
                action_description=f'Scheduled compliance assessment for {framework.name}',
                risk_level='MEDIUM',
                user=request.user,
                employee=Employee.objects.get(user=request.user),
                resource_type='ComplianceFramework',
                resource_id=str(framework.id),
                resource_name=framework.name,
                new_values={'next_assessment_due': assessment_date.isoformat()},
                ip_address=request.META.get('REMOTE_ADDR'),
                compliance_category='COMPLIANCE_MANAGEMENT'
            )

            return Response({
                'message': f'Assessment scheduled for {assessment_date}',
                'next_assessment_due': assessment_date
            })

        except ValueError:
            return Response({'error': 'Invalid date format. Use YYYY-MM-DD'}, status=400)

    @action(detail=False, methods=['get'])
    def compliance_dashboard(self, request):
        """Get compliance dashboard data"""
        frameworks = self.get_queryset()

        # Overall compliance statistics
        total_frameworks = frameworks.count()
        active_frameworks = frameworks.filter(is_active=True).count()

        # Average compliance score
        avg_compliance = frameworks.aggregate(
            avg_score=Avg('compliance_score')
        )['avg_score'] or 0

        # Frameworks needing assessment
        overdue_assessments = frameworks.filter(
            next_assessment_due__lt=timezone.now().date()
        ).count()

        # Framework status breakdown
        status_breakdown = frameworks.values('implementation_status').annotate(
            count=Count('id')
        ).order_by('implementation_status')

        # Top compliance frameworks by score
        top_frameworks = frameworks.order_by('-compliance_score')[:5].values(
            'name', 'framework_type', 'compliance_score'
        )

        return Response({
            'total_frameworks': total_frameworks,
            'active_frameworks': active_frameworks,
            'average_compliance_score': round(avg_compliance, 2),
            'overdue_assessments': overdue_assessments,
            'status_breakdown': list(status_breakdown),
            'top_frameworks': list(top_frameworks)
        })


class ComplianceControlViewSet(viewsets.ModelViewSet):
    """
    Compliance Control management API
    """
    queryset = ComplianceControl.objects.all()
    serializer_class = ComplianceControlSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['framework', 'control_type', 'status', 'risk_level', 'control_owner']
    search_fields = ['control_id', 'name', 'name_ar', 'description']
    ordering_fields = ['priority', 'risk_level', 'status', 'last_tested_date']
    ordering = ['framework', 'priority', 'control_id']

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """Update control implementation status"""
        control = self.get_object()
        new_status = request.data.get('status')
        notes = request.data.get('implementation_notes', '')

        if new_status not in ['NOT_IMPLEMENTED', 'PARTIALLY_IMPLEMENTED', 'IMPLEMENTED', 'NEEDS_IMPROVEMENT', 'NOT_APPLICABLE']:
            return Response({'error': 'Invalid status'}, status=400)

        old_status = control.status
        control.status = new_status
        control.implementation_notes = notes
        control.save()

        # Update framework compliance score
        framework = control.framework
        total_controls = framework.controls.count()
        implemented_controls = framework.controls.filter(status='IMPLEMENTED').count()
        framework.compliance_score = (implemented_controls / total_controls) * 100 if total_controls > 0 else 0
        framework.save()

        # Log status change
        AuditTrail.objects.create(
            action_type='UPDATE',
            action_description=f'Updated control {control.control_id} status from {old_status} to {new_status}',
            risk_level='MEDIUM',
            user=request.user,
            employee=Employee.objects.get(user=request.user),
            resource_type='ComplianceControl',
            resource_id=str(control.id),
            resource_name=f"{control.framework.name} - {control.control_id}",
            old_values={'status': old_status},
            new_values={'status': new_status, 'implementation_notes': notes},
            ip_address=request.META.get('REMOTE_ADDR'),
            compliance_category='COMPLIANCE_CONTROL'
        )

        return Response({
            'message': f'Control status updated to {new_status}',
            'framework_compliance_score': framework.compliance_score
        })

    @action(detail=True, methods=['post'])
    def record_test(self, request, pk=None):
        """Record control testing results"""
        control = self.get_object()
        test_results = request.data.get('test_results', '')
        test_date = request.data.get('test_date')

        if not test_date:
            test_date = timezone.now().date()
        else:
            from datetime import datetime
            test_date = datetime.strptime(test_date, '%Y-%m-%d').date()

        control.last_tested_date = test_date
        control.test_results = test_results
        control.save()

        # Log test recording
        AuditTrail.objects.create(
            action_type='UPDATE',
            action_description=f'Recorded test results for control {control.control_id}',
            risk_level='LOW',
            user=request.user,
            employee=Employee.objects.get(user=request.user),
            resource_type='ComplianceControl',
            resource_id=str(control.id),
            resource_name=f"{control.framework.name} - {control.control_id}",
            new_values={'last_tested_date': test_date.isoformat(), 'test_results': test_results},
            ip_address=request.META.get('REMOTE_ADDR'),
            compliance_category='COMPLIANCE_TESTING'
        )

        return Response({
            'message': 'Test results recorded successfully',
            'last_tested_date': test_date,
            'next_test_due': control.is_test_due()
        })

    @action(detail=False, methods=['get'])
    def control_stats(self, request):
        """Get compliance control statistics"""
        framework_id = request.query_params.get('framework_id')

        queryset = self.get_queryset()
        if framework_id:
            queryset = queryset.filter(framework_id=framework_id)

        stats = queryset.aggregate(
            total_controls=Count('id'),
            implemented_controls=Count('id', filter=Q(status='IMPLEMENTED')),
            partially_implemented=Count('id', filter=Q(status='PARTIALLY_IMPLEMENTED')),
            not_implemented=Count('id', filter=Q(status='NOT_IMPLEMENTED')),
            high_risk_controls=Count('id', filter=Q(risk_level='HIGH')),
            critical_risk_controls=Count('id', filter=Q(risk_level='CRITICAL'))
        )

        # Controls needing testing
        overdue_tests = queryset.filter(
            models.Q(last_tested_date__isnull=True) |
            models.Q(last_tested_date__lt=timezone.now().date() - timedelta(days=365))
        ).count()

        # Status breakdown
        status_breakdown = queryset.values('status').annotate(
            count=Count('id')
        ).order_by('status')

        return Response({
            'total_controls': stats['total_controls'],
            'implemented_controls': stats['implemented_controls'],
            'implementation_rate': round((stats['implemented_controls'] / stats['total_controls']) * 100, 2) if stats['total_controls'] > 0 else 0,
            'partially_implemented': stats['partially_implemented'],
            'not_implemented': stats['not_implemented'],
            'high_risk_controls': stats['high_risk_controls'],
            'critical_risk_controls': stats['critical_risk_controls'],
            'overdue_tests': overdue_tests,
            'status_breakdown': list(status_breakdown)
        })


class DataClassificationViewSet(viewsets.ModelViewSet):
    """
    Data Classification management API
    """
    queryset = DataClassification.objects.all()
    serializer_class = DataClassificationSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['classification_level', 'encryption_required', 'access_logging_required']
    search_fields = ['name', 'name_ar', 'description']
    ordering_fields = ['name', 'classification_level', 'created_at']
    ordering = ['classification_level', 'name']

    def perform_create(self, serializer):
        employee = Employee.objects.get(user=self.request.user)
        serializer.save(created_by=employee)

    @action(detail=False, methods=['get'])
    def classification_stats(self, request):
        """Get data classification statistics"""
        stats = self.get_queryset().aggregate(
            total_classifications=Count('id'),
            encrypted_classifications=Count('id', filter=Q(encryption_required=True)),
            logged_classifications=Count('id', filter=Q(access_logging_required=True)),
            approval_required_classifications=Count('id', filter=Q(approval_required_for_access=True))
        )

        # Classification level breakdown
        level_breakdown = self.get_queryset().values('classification_level').annotate(
            count=Count('id')
        ).order_by('classification_level')

        return Response({
            'total_classifications': stats['total_classifications'],
            'encrypted_classifications': stats['encrypted_classifications'],
            'logged_classifications': stats['logged_classifications'],
            'approval_required_classifications': stats['approval_required_classifications'],
            'level_breakdown': list(level_breakdown)
        })


class SecurityAlertViewSet(viewsets.ModelViewSet):
    """
    Security Alert management API
    """
    queryset = SecurityAlert.objects.all()
    serializer_class = SecurityAlertSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['alert_type', 'severity', 'status', 'assigned_to', 'escalated']
    search_fields = ['alert_id', 'title', 'description']
    ordering_fields = ['severity', 'detected_at', 'risk_score']
    ordering = ['-severity', '-detected_at']

    def perform_create(self, serializer):
        import uuid

        # Generate unique alert ID
        alert_id = f"ALERT-{timezone.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"

        serializer.save(alert_id=alert_id)

    @action(detail=True, methods=['post'])
    def acknowledge(self, request, pk=None):
        """Acknowledge security alert"""
        alert = self.get_object()

        if alert.status != 'OPEN':
            return Response({'error': 'Alert is not in OPEN status'}, status=400)

        alert.status = 'ACKNOWLEDGED'
        alert.acknowledged_at = timezone.now()
        alert.assigned_to = Employee.objects.get(user=request.user)
        alert.save()

        # Log acknowledgment
        AuditTrail.objects.create(
            action_type='UPDATE',
            action_description=f'Acknowledged security alert {alert.alert_id}',
            risk_level='MEDIUM',
            user=request.user,
            employee=Employee.objects.get(user=request.user),
            resource_type='SecurityAlert',
            resource_id=str(alert.id),
            resource_name=alert.alert_id,
            new_values={'status': 'ACKNOWLEDGED', 'acknowledged_at': alert.acknowledged_at.isoformat()},
            ip_address=request.META.get('REMOTE_ADDR'),
            compliance_category='SECURITY_MONITORING'
        )

        return Response({
            'message': 'Alert acknowledged successfully',
            'acknowledged_at': alert.acknowledged_at,
            'response_time_minutes': alert.get_response_time().total_seconds() / 60 if alert.get_response_time() else None
        })

    @action(detail=True, methods=['post'])
    def escalate(self, request, pk=None):
        """Escalate security alert"""
        alert = self.get_object()
        escalate_to_id = request.data.get('escalate_to_id')
        escalation_reason = request.data.get('reason', '')

        try:
            escalate_to = Employee.objects.get(id=escalate_to_id)

            alert.escalated = True
            alert.escalated_at = timezone.now()
            alert.escalated_to = escalate_to
            alert.save()

            # Log escalation
            AuditTrail.objects.create(
                action_type='UPDATE',
                action_description=f'Escalated security alert {alert.alert_id} to {escalate_to.user.get_full_name()}',
                risk_level='HIGH',
                user=request.user,
                employee=Employee.objects.get(user=request.user),
                resource_type='SecurityAlert',
                resource_id=str(alert.id),
                resource_name=alert.alert_id,
                new_values={
                    'escalated': True,
                    'escalated_to': escalate_to.id,
                    'escalated_at': alert.escalated_at.isoformat()
                },
                additional_context={'escalation_reason': escalation_reason},
                ip_address=request.META.get('REMOTE_ADDR'),
                compliance_category='SECURITY_ESCALATION'
            )

            return Response({
                'message': f'Alert escalated to {escalate_to.user.get_full_name()}',
                'escalated_at': alert.escalated_at
            })

        except Employee.DoesNotExist:
            return Response({'error': 'Invalid escalation target'}, status=400)

    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        """Resolve security alert"""
        alert = self.get_object()
        resolution_notes = request.data.get('resolution_notes', '')

        alert.status = 'RESOLVED'
        alert.resolved_at = timezone.now()
        alert.resolution_notes = resolution_notes
        alert.save()

        # Log resolution
        AuditTrail.objects.create(
            action_type='UPDATE',
            action_description=f'Resolved security alert {alert.alert_id}',
            risk_level='MEDIUM',
            user=request.user,
            employee=Employee.objects.get(user=request.user),
            resource_type='SecurityAlert',
            resource_id=str(alert.id),
            resource_name=alert.alert_id,
            new_values={
                'status': 'RESOLVED',
                'resolved_at': alert.resolved_at.isoformat(),
                'resolution_notes': resolution_notes
            },
            ip_address=request.META.get('REMOTE_ADDR'),
            compliance_category='SECURITY_RESOLUTION'
        )

        return Response({
            'message': 'Alert resolved successfully',
            'resolved_at': alert.resolved_at,
            'resolution_time_hours': alert.get_resolution_time().total_seconds() / 3600 if alert.get_resolution_time() else None
        })

    @action(detail=False, methods=['get'])
    def alert_stats(self, request):
        """Get security alert statistics"""
        from django.db.models import Avg

        stats = self.get_queryset().aggregate(
            total_alerts=Count('id'),
            open_alerts=Count('id', filter=Q(status='OPEN')),
            critical_alerts=Count('id', filter=Q(severity='CRITICAL')),
            high_alerts=Count('id', filter=Q(severity='HIGH')),
            escalated_alerts=Count('id', filter=Q(escalated=True)),
            resolved_alerts=Count('id', filter=Q(status='RESOLVED'))
        )

        # Average response and resolution times
        acknowledged_alerts = self.get_queryset().filter(acknowledged_at__isnull=False)
        resolved_alerts = self.get_queryset().filter(resolved_at__isnull=False)

        avg_response_time = 0
        avg_resolution_time = 0

        if acknowledged_alerts.exists():
            response_times = [
                (alert.acknowledged_at - alert.detected_at).total_seconds() / 60
                for alert in acknowledged_alerts
                if alert.acknowledged_at and alert.detected_at
            ]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0

        if resolved_alerts.exists():
            resolution_times = [
                (alert.resolved_at - alert.detected_at).total_seconds() / 3600
                for alert in resolved_alerts
                if alert.resolved_at and alert.detected_at
            ]
            avg_resolution_time = sum(resolution_times) / len(resolution_times) if resolution_times else 0

        # Recent alerts (last 24 hours)
        recent_cutoff = timezone.now() - timedelta(hours=24)
        recent_alerts = self.get_queryset().filter(detected_at__gte=recent_cutoff).count()

        # Alert type breakdown
        type_breakdown = self.get_queryset().values('alert_type').annotate(
            count=Count('id')
        ).order_by('-count')

        # Severity breakdown
        severity_breakdown = self.get_queryset().values('severity').annotate(
            count=Count('id')
        ).order_by('severity')

        return Response({
            'total_alerts': stats['total_alerts'],
            'open_alerts': stats['open_alerts'],
            'critical_alerts': stats['critical_alerts'],
            'high_alerts': stats['high_alerts'],
            'escalated_alerts': stats['escalated_alerts'],
            'resolved_alerts': stats['resolved_alerts'],
            'recent_alerts_24h': recent_alerts,
            'average_response_time_minutes': round(avg_response_time, 2),
            'average_resolution_time_hours': round(avg_resolution_time, 2),
            'alert_type_breakdown': list(type_breakdown),
            'severity_breakdown': list(severity_breakdown)
        })

    @action(detail=False, methods=['post'])
    def bulk_acknowledge(self, request):
        """Bulk acknowledge multiple alerts"""
        alert_ids = request.data.get('alert_ids', [])

        if not alert_ids:
            return Response({'error': 'alert_ids required'}, status=400)

        alerts = self.get_queryset().filter(id__in=alert_ids, status='OPEN')
        employee = Employee.objects.get(user=request.user)

        updated_count = 0
        for alert in alerts:
            alert.status = 'ACKNOWLEDGED'
            alert.acknowledged_at = timezone.now()
            alert.assigned_to = employee
            alert.save()
            updated_count += 1

            # Log each acknowledgment
            AuditTrail.objects.create(
                action_type='UPDATE',
                action_description=f'Bulk acknowledged security alert {alert.alert_id}',
                risk_level='MEDIUM',
                user=request.user,
                employee=employee,
                resource_type='SecurityAlert',
                resource_id=str(alert.id),
                resource_name=alert.alert_id,
                new_values={'status': 'ACKNOWLEDGED'},
                ip_address=request.META.get('REMOTE_ADDR'),
                compliance_category='SECURITY_MONITORING'
            )

        return Response({
            'message': f'Acknowledged {updated_count} alerts',
            'updated_count': updated_count
        })
