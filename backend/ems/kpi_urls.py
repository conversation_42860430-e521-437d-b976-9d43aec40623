"""
Enhanced KPI URL Configuration
Routes for enterprise-grade automated KPI management APIs

IMPORTANT: Manual KPI entry has been disabled to enforce enterprise automation.
All KPI values are now calculated automatically from operational data.

Primary Endpoints (Use These):
- /enhanced/kpis/ - Automated KPI management
- /enhanced/values/ - Read-only KPI values
- /enhanced-dashboard/ - Enterprise KPI dashboard

Legacy Endpoints (Deprecated - Read-only):
- /kpis/ - Legacy KPI access (read-only)
- /values/ - Legacy KPI values (read-only)
- /dashboard/ - Legacy dashboard
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    KPICategoryViewSet, KPIValueViewSet,
    KPITargetViewSet, KPIAlertViewSet, kpi_dashboard
)
from .enhanced_kpi_views import (
    EnhancedKPIViewSet, EnhancedKPIValueViewSet, enhanced_kpi_dashboard
)

# Create router for enhanced KPI viewsets (PRIMARY SYSTEM)
enhanced_router = DefaultRouter()
enhanced_router.register(r'kpis', EnhancedKPIViewSet, basename='enhanced-kpis')
enhanced_router.register(r'values', EnhancedKPIValueViewSet, basename='enhanced-kpi-values')

# Create router for legacy KPI viewsets (DEPRECATED - Read-only only)
legacy_router = DefaultRouter()
legacy_router.register(r'categories', KPICategoryViewSet, basename='kpi-categories')
# REMOVED: KPIViewSet - Use EnhancedKPIViewSet instead
legacy_router.register(r'values', KPIValueViewSet, basename='kpi-values')
legacy_router.register(r'targets', KPITargetViewSet, basename='kpi-targets')
legacy_router.register(r'alerts', KPIAlertViewSet, basename='kpi-alerts')

urlpatterns = [
    # PRIMARY SYSTEM: Enhanced KPI endpoints (use these for all new development)
    path('enhanced-dashboard/', enhanced_kpi_dashboard, name='enhanced-kpi-dashboard'),
    path('enhanced/', include(enhanced_router.urls)),

    # DEPRECATED: Legacy endpoints (read-only access only for backward compatibility)
    path('dashboard/', kpi_dashboard, name='kpi-dashboard'),
    path('', include(legacy_router.urls)),
]
