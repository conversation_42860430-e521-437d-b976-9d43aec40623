"""
Enhanced Enterprise KPI Calculation Engine
Eliminates manual entry and implements enterprise-grade automatic calculation.

Key Enhancements:
1. Comprehensive KPI coverage (30+ enterprise KPIs)
2. Advanced caching and performance optimization
3. Data quality monitoring and validation
4. Scheduled batch recalculation
5. Enhanced error handling and logging
"""

from django.db.models import Sum, Count, Avg, Q, F, Case, When, Value, Max, Min
from django.utils import timezone
from django.core.cache import cache
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
import logging
import hashlib
import json

from .models import (
    KPI, KPIValue, Employee, Project, Task, CustomerInvoice, 
    Expense, Attendance, LeaveRequest, Department, Payment,
    Asset, AssetMaintenance, ComplianceControl, SecurityAlert
)

logger = logging.getLogger(__name__)


class EnhancedKPICalculationEngine:
    """
    Enhanced enterprise KPI calculation engine with comprehensive automation.
    Eliminates manual entry and provides enterprise-grade KPI management.
    """
    
    def __init__(self):
        self.cache_timeout = 300  # 5 minutes cache
        self.calculation_methods = {
            # HR KPIs (Enhanced)
            'EMPLOYEE_TURNOVER_RATE': self._calculate_employee_turnover_rate,
            'EMPLOYEE_SATISFACTION_SCORE': self._calculate_employee_satisfaction_score,
            'AVERAGE_ATTENDANCE_RATE': self._calculate_attendance_rate,
            'attendance_rate': self._calculate_attendance_rate,  # Alias for backward compatibility
            'TIME_TO_FILL_POSITIONS': self._calculate_time_to_fill,
            'EMPLOYEE_PRODUCTIVITY_SCORE': self._calculate_employee_productivity,
            'TRAINING_COMPLETION_RATE': self._calculate_training_completion_rate,
            'EMPLOYEE_RETENTION_RATE': self._calculate_employee_retention_rate,
            'ABSENTEEISM_RATE': self._calculate_absenteeism_rate,
            'OVERTIME_HOURS_RATIO': self._calculate_overtime_ratio,
            
            # Financial KPIs (Enhanced)
            'MONTHLY_REVENUE': self._calculate_monthly_revenue,
            'PROFIT_MARGIN': self._calculate_profit_margin,
            'AVERAGE_INVOICE_VALUE': self._calculate_average_invoice_value,
            'CASH_FLOW': self._calculate_cash_flow,
            'REVENUE_GROWTH_RATE': self._calculate_revenue_growth_rate,
            'COST_PER_EMPLOYEE': self._calculate_cost_per_employee,
            'ACCOUNTS_RECEIVABLE_TURNOVER': self._calculate_ar_turnover,
            'EXPENSE_RATIO': self._calculate_expense_ratio,
            'BUDGET_VARIANCE': self._calculate_budget_variance,
            'ROI': self._calculate_roi,
            
            # Operations KPIs (Enhanced)
            'PROJECT_COMPLETION_RATE': self._calculate_project_completion_rate,
            'TASK_COMPLETION_RATE': self._calculate_task_completion_rate,
            'AVERAGE_PROJECT_DURATION': self._calculate_average_project_duration,
            'PROJECT_BUDGET_VARIANCE': self._calculate_project_budget_variance,
            'RESOURCE_UTILIZATION_RATE': self._calculate_resource_utilization,
            'ON_TIME_DELIVERY_RATE': self._calculate_on_time_delivery,
            'PROJECT_SUCCESS_RATE': self._calculate_project_success_rate,
            'CAPACITY_UTILIZATION': self._calculate_capacity_utilization,
            
            # Customer KPIs (Enhanced)
            'CUSTOMER_ACQUISITION_RATE': self._calculate_customer_acquisition_rate,
            'CUSTOMER_RETENTION_RATE': self._calculate_customer_retention_rate,
            'AVERAGE_DEAL_SIZE': self._calculate_average_deal_size,
            'CUSTOMER_LIFETIME_VALUE': self._calculate_customer_lifetime_value,
            'INVOICE_COLLECTION_TIME': self._calculate_collection_time,
            
            # Asset & Compliance KPIs (New)
            'ASSET_UTILIZATION_RATE': self._calculate_asset_utilization,
            'MAINTENANCE_COST_RATIO': self._calculate_maintenance_cost_ratio,
            'COMPLIANCE_SCORE': self._calculate_compliance_score,
            'SECURITY_INCIDENT_RATE': self._calculate_security_incident_rate,
            'SYSTEM_UPTIME': self._calculate_system_uptime,
        }
    
    def get_cache_key(self, kpi_id: str, period_start: datetime, period_end: datetime) -> str:
        """Generate cache key for KPI calculation"""
        key_data = f"{kpi_id}_{period_start.isoformat()}_{period_end.isoformat()}"
        return f"kpi_calc_{hashlib.md5(key_data.encode()).hexdigest()}"
    
    def calculate_kpi_with_cache(self, kpi: KPI, period_start: datetime = None, period_end: datetime = None) -> Optional[Decimal]:
        """
        Calculate KPI with intelligent caching for performance optimization.
        """
        if not period_start:
            period_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        if not period_end:
            period_end = timezone.now()
        
        # Check cache first
        cache_key = self.get_cache_key(str(kpi.id), period_start, period_end)
        cached_value = cache.get(cache_key)
        
        if cached_value is not None:
            logger.info(f"Using cached value for KPI {kpi.name}: {cached_value}")
            return Decimal(str(cached_value))
        
        # Calculate fresh value
        calculated_value = self.calculate_kpi(kpi, period_start, period_end)
        
        # Cache the result
        if calculated_value is not None:
            cache.set(cache_key, float(calculated_value), self.cache_timeout)
        
        return calculated_value
    
    def calculate_kpi(self, kpi: KPI, period_start: datetime = None, period_end: datetime = None) -> Optional[Decimal]:
        """
        Calculate a single KPI value with enhanced error handling and validation.
        """
        if not period_start:
            period_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        if not period_end:
            period_end = timezone.now()
            
        calculation_method = kpi.calculation_method
        if calculation_method not in self.calculation_methods:
            logger.warning(f"No calculation method found for KPI: {kpi.name} (method: {calculation_method})")
            return None
            
        try:
            # Validate data availability before calculation
            if not self._validate_data_availability(calculation_method, period_start, period_end):
                logger.warning(f"Insufficient data for KPI calculation: {kpi.name}")
                return None
            
            value = self.calculation_methods[calculation_method](period_start, period_end)
            
            # Validate calculated value
            if value is not None and self._validate_calculated_value(value, kpi):
                logger.info(f"Calculated KPI {kpi.name}: {value}")
                return Decimal(str(value))
            else:
                logger.warning(f"Invalid calculated value for KPI {kpi.name}: {value}")
                return None
                
        except Exception as e:
            logger.error(f"Error calculating KPI {kpi.name}: {str(e)}", exc_info=True)
            return None
    
    def _validate_data_availability(self, calculation_method: str, start_date: datetime, end_date: datetime) -> bool:
        """
        Validate that sufficient data exists for reliable KPI calculation.
        """
        data_requirements = {
            'EMPLOYEE_TURNOVER_RATE': lambda: Employee.objects.filter(created_at__lte=end_date).exists(),
            'MONTHLY_REVENUE': lambda: CustomerInvoice.objects.filter(
                invoice_date__range=[start_date.date(), end_date.date()]
            ).exists(),
            'PROJECT_COMPLETION_RATE': lambda: Project.objects.filter(
                created_at__lte=end_date
            ).exists(),
            # Add more validation rules as needed
        }
        
        validator = data_requirements.get(calculation_method)
        if validator:
            return validator()
        
        return True  # Default to True for methods without specific validation
    
    def _validate_calculated_value(self, value: float, kpi: KPI) -> bool:
        """
        Validate that calculated KPI value is reasonable and within expected bounds.
        """
        if value is None or not isinstance(value, (int, float)):
            return False
        
        # Check for reasonable bounds based on measurement type
        if kpi.measurement_type == 'PERCENTAGE':
            return 0 <= value <= 100
        elif kpi.measurement_type == 'CURRENCY':
            return value >= 0  # Negative revenue/profit is possible but should be flagged
        elif kpi.measurement_type == 'TIME':
            return value >= 0
        
        return True  # Default validation passes
    
    def calculate_all_kpis_enhanced(self, period_start: datetime = None, period_end: datetime = None) -> Dict[str, Any]:
        """
        Enhanced batch calculation of all active KPIs with comprehensive reporting.
        """
        if not period_start:
            period_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        if not period_end:
            period_end = timezone.now()
            
        results = {
            'calculated': 0,
            'failed': 0,
            'cached': 0,
            'updated_kpis': [],
            'failed_kpis': [],
            'data_quality_issues': [],
            'period_start': period_start,
            'period_end': period_end,
            'execution_time': None
        }
        
        start_time = timezone.now()
        active_kpis = KPI.objects.filter(status='ACTIVE', calculation_method__isnull=False)
        
        for kpi in active_kpis:
            try:
                calculated_value = self.calculate_kpi_with_cache(kpi, period_start, period_end)
                
                if calculated_value is not None:
                    # Create or update KPI value with enhanced metadata
                    kpi_value, created = KPIValue.objects.update_or_create(
                        kpi=kpi,
                        period_start=period_start,
                        period_end=period_end,
                        defaults={
                            'value': calculated_value,
                            'recorded_by': kpi.created_by,
                            'is_estimated': False,
                            'confidence_level': Decimal('100.0'),
                            'data_quality_score': self._calculate_data_quality_score(kpi, period_start, period_end),
                            'notes': f'Automatically calculated by Enhanced KPI Engine',
                            'source_data': {
                                'calculation_method': kpi.calculation_method,
                                'engine': 'EnhancedKPICalculationEngine',
                                'timestamp': timezone.now().isoformat(),
                                'data_sources': self._get_data_sources(kpi.calculation_method),
                                'validation_passed': True
                            }
                        }
                    )
                    
                    # Update KPI current value
                    kpi.current_value = calculated_value
                    kpi.last_updated = timezone.now()
                    kpi.save(update_fields=['current_value', 'last_updated'])
                    
                    results['calculated'] += 1
                    results['updated_kpis'].append({
                        'kpi_id': kpi.id,
                        'name': kpi.name,
                        'value': float(calculated_value),
                        'created': created,
                        'data_quality_score': float(kpi_value.data_quality_score)
                    })
                else:
                    results['failed'] += 1
                    results['failed_kpis'].append({
                        'kpi_id': kpi.id,
                        'name': kpi.name,
                        'error': 'Calculation returned None'
                    })
                    
            except Exception as e:
                results['failed'] += 1
                results['failed_kpis'].append({
                    'kpi_id': kpi.id,
                    'name': kpi.name,
                    'error': str(e)
                })
                logger.error(f"Failed to calculate KPI {kpi.name}: {str(e)}")
        
        results['execution_time'] = (timezone.now() - start_time).total_seconds()
        logger.info(f"Enhanced KPI calculation completed: {results['calculated']} calculated, {results['failed']} failed in {results['execution_time']:.2f}s")
        
        return results
    
    def _calculate_data_quality_score(self, kpi: KPI, start_date: datetime, end_date: datetime) -> Decimal:
        """
        Calculate data quality score based on data completeness and freshness.
        """
        # This is a simplified implementation - can be enhanced based on specific requirements
        base_score = Decimal('100.0')
        
        # Check data freshness (reduce score if data is old)
        data_age_days = (timezone.now() - end_date).days
        if data_age_days > 7:
            base_score -= Decimal('10.0')
        if data_age_days > 30:
            base_score -= Decimal('20.0')
        
        return max(base_score, Decimal('0.0'))
    
    def _get_data_sources(self, calculation_method: str) -> List[str]:
        """
        Get list of data sources used for a specific calculation method.
        """
        data_source_mapping = {
            'EMPLOYEE_TURNOVER_RATE': ['Employee'],
            'MONTHLY_REVENUE': ['CustomerInvoice'],
            'PROJECT_COMPLETION_RATE': ['Project'],
            'TASK_COMPLETION_RATE': ['Task'],
            'AVERAGE_ATTENDANCE_RATE': ['Attendance'],
            'PROFIT_MARGIN': ['CustomerInvoice', 'Expense'],
            'CASH_FLOW': ['Payment', 'Expense'],
            # Add more mappings as needed
        }
        
        return data_source_mapping.get(calculation_method, ['Unknown'])

    # ==================== HR KPI CALCULATIONS ====================

    def _calculate_employee_turnover_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate employee turnover rate as percentage"""
        # Get employees who left during the period (became inactive)
        employees_left = Employee.objects.filter(
            is_active=False,
            updated_at__range=[start_date, end_date]
        ).count()

        # Get average number of employees during the period
        total_employees = Employee.objects.filter(is_active=True).count()

        if total_employees == 0:
            return 0.0

        return (employees_left / total_employees) * 100

    def _calculate_attendance_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate average attendance rate as percentage"""
        attendance_records = Attendance.objects.filter(
            date__range=[start_date.date(), end_date.date()]
        )

        if not attendance_records.exists():
            return 0.0

        total_records = attendance_records.count()
        present_records = attendance_records.filter(is_present=True).count()

        return (present_records / total_records) * 100 if total_records > 0 else 0.0

    def _calculate_employee_satisfaction_score(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate employee satisfaction score based on multiple workplace indicators"""
        total_employees = Employee.objects.filter(is_active=True).count()

        if total_employees == 0:
            return 0.0

        satisfaction_score = 0.0
        factors_count = 0

        # Factor 1: Attendance rate (higher attendance = higher satisfaction)
        attendance_rate = self._calculate_attendance_rate(start_date, end_date)
        if attendance_rate > 0:
            satisfaction_score += min(attendance_rate, 100) / 100 * 25  # Max 25 points
            factors_count += 1

        # Factor 2: Leave request approval rate
        total_leave_requests = LeaveRequest.objects.filter(
            start_date__range=[start_date.date(), end_date.date()]
        ).count()
        approved_leave_requests = LeaveRequest.objects.filter(
            start_date__range=[start_date.date(), end_date.date()],
            status='APPROVED'
        ).count()

        if total_leave_requests > 0:
            approval_rate = (approved_leave_requests / total_leave_requests) * 100
            satisfaction_score += approval_rate / 100 * 25  # Max 25 points
            factors_count += 1

        # Factor 3: Task completion rate (productivity satisfaction)
        task_completion_rate = self._calculate_task_completion_rate(start_date, end_date)
        if task_completion_rate > 0:
            satisfaction_score += min(task_completion_rate, 100) / 100 * 25  # Max 25 points
            factors_count += 1

        # Factor 4: Low turnover indicates satisfaction
        turnover_rate = self._calculate_employee_turnover_rate(start_date, end_date)
        turnover_satisfaction = max(0, 100 - turnover_rate * 10)
        satisfaction_score += turnover_satisfaction / 100 * 25  # Max 25 points
        factors_count += 1

        return satisfaction_score / factors_count if factors_count > 0 else 0.0

    def _calculate_time_to_fill(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate average time to fill positions based on hiring data"""
        new_employees = Employee.objects.filter(
            created_at__range=[start_date, end_date],
            is_active=True
        )

        if not new_employees.exists():
            return 0.0

        # Simplified calculation - assume 30 days average time to fill
        # In a real implementation, this would track from job posting to hire
        return 30.0

    def _calculate_employee_productivity(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate employee productivity score based on task completion"""
        total_employees = Employee.objects.filter(is_active=True).count()

        if total_employees == 0:
            return 0.0

        # Get tasks assigned to employees in the period
        completed_tasks = Task.objects.filter(
            updated_at__range=[start_date, end_date],
            status='COMPLETED',
            assigned_to__isnull=False
        ).count()

        total_tasks = Task.objects.filter(
            created_at__lte=end_date,
            assigned_to__isnull=False
        ).count()

        if total_tasks == 0:
            return 0.0

        completion_rate = (completed_tasks / total_tasks) * 100
        return min(completion_rate, 100.0)

    def _calculate_training_completion_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate training completion rate based on employee development activities"""
        # Since we don't have a Training model yet, calculate based on employee development
        total_employees = Employee.objects.filter(is_active=True).count()

        if total_employees == 0:
            return 0.0

        # Use employee certifications as a proxy for training completion
        employees_with_recent_certs = Employee.objects.filter(
            is_active=True,
            updated_at__gte=start_date  # Employees who updated their profiles (potential training)
        ).count()

        # Calculate completion rate based on profile updates (proxy for training activity)
        completion_rate = (employees_with_recent_certs / total_employees) * 100
        return min(completion_rate, 100.0)

    def _calculate_employee_retention_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate employee retention rate (inverse of turnover)"""
        turnover_rate = self._calculate_employee_turnover_rate(start_date, end_date)
        return 100.0 - turnover_rate

    def _calculate_absenteeism_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate absenteeism rate as percentage"""
        attendance_rate = self._calculate_attendance_rate(start_date, end_date)
        return 100.0 - attendance_rate

    def _calculate_overtime_ratio(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate overtime hours ratio based on attendance data"""
        # Calculate based on attendance patterns and working hours
        attendance_records = Attendance.objects.filter(
            date__range=[start_date.date(), end_date.date()],
            is_present=True
        )

        if not attendance_records.exists():
            return 0.0

        # Estimate overtime based on late check-outs (after 6 PM)
        # This is a simplified calculation - in real implementation would use actual hours
        total_work_days = attendance_records.count()

        # Count records where employees might have worked overtime
        # (This is an estimation - real implementation would track actual hours)
        potential_overtime_days = attendance_records.filter(
            check_out_time__isnull=False,
            check_out_time__hour__gte=18  # After 6 PM
        ).count()

        if total_work_days == 0:
            return 0.0

        return (potential_overtime_days / total_work_days) * 100

    # ==================== FINANCIAL KPI CALCULATIONS ====================

    def _calculate_monthly_revenue(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate total revenue from paid customer invoices"""
        revenue = CustomerInvoice.objects.filter(
            status='PAID',
            invoice_date__range=[start_date.date(), end_date.date()]
        ).aggregate(total=Sum('total_amount'))['total']

        return float(revenue) if revenue else 0.0

    def _calculate_profit_margin(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate profit margin percentage"""
        revenue = self._calculate_monthly_revenue(start_date, end_date)

        expenses = Expense.objects.filter(
            expense_date__range=[start_date.date(), end_date.date()]
        ).aggregate(total=Sum('amount'))['total']

        expenses = float(expenses) if expenses else 0.0

        if revenue == 0:
            return 0.0

        profit = revenue - expenses
        return (profit / revenue) * 100

    def _calculate_average_invoice_value(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate average invoice value"""
        invoices = CustomerInvoice.objects.filter(
            invoice_date__range=[start_date.date(), end_date.date()]
        )

        if not invoices.exists():
            return 0.0

        avg_value = invoices.aggregate(avg=Avg('total_amount'))['avg']
        return float(avg_value) if avg_value else 0.0

    def _calculate_cash_flow(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate net cash flow based on invoices and expenses"""
        try:
            # Cash inflows from customer invoices (approximation)
            inflows = CustomerInvoice.objects.filter(
                invoice_date__range=[start_date.date(), end_date.date()]
            ).aggregate(total=Sum('total_amount'))['total']

            # Cash outflows from expenses
            expense_outflows = Expense.objects.filter(
                expense_date__range=[start_date.date(), end_date.date()]
            ).aggregate(total=Sum('amount'))['total']

            inflows = float(inflows) if inflows else 0.0
            expense_outflows = float(expense_outflows) if expense_outflows else 0.0

            return inflows - expense_outflows
        except Exception as e:
            logger.error(f"Error calculating cash flow: {e}")
            return 0.0

    def _calculate_revenue_growth_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate revenue growth rate compared to previous period"""
        current_revenue = self._calculate_monthly_revenue(start_date, end_date)

        # Calculate previous period revenue
        period_length = end_date - start_date
        prev_start = start_date - period_length
        prev_end = start_date

        previous_revenue = self._calculate_monthly_revenue(prev_start, prev_end)

        if previous_revenue == 0:
            return 0.0 if current_revenue == 0 else 100.0

        return ((current_revenue - previous_revenue) / previous_revenue) * 100

    def _calculate_cost_per_employee(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate total cost per employee"""
        total_expenses = Expense.objects.filter(
            expense_date__range=[start_date.date(), end_date.date()]
        ).aggregate(total=Sum('amount'))['total']

        total_employees = Employee.objects.filter(is_active=True).count()

        if total_employees == 0:
            return 0.0

        expenses = float(total_expenses) if total_expenses else 0.0
        return expenses / total_employees

    def _calculate_ar_turnover(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate accounts receivable turnover ratio"""
        # Revenue for the period
        revenue = self._calculate_monthly_revenue(start_date, end_date)

        # Average accounts receivable (unpaid invoices)
        unpaid_invoices = CustomerInvoice.objects.filter(
            status__in=['PENDING', 'OVERDUE'],
            invoice_date__lte=end_date.date()
        ).aggregate(total=Sum('total_amount'))['total']

        avg_ar = float(unpaid_invoices) if unpaid_invoices else 0.0

        if avg_ar == 0:
            return 0.0

        return revenue / avg_ar

    def _calculate_expense_ratio(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate expense ratio as percentage of revenue"""
        revenue = self._calculate_monthly_revenue(start_date, end_date)

        expenses = Expense.objects.filter(
            expense_date__range=[start_date.date(), end_date.date()]
        ).aggregate(total=Sum('amount'))['total']

        expenses = float(expenses) if expenses else 0.0

        if revenue == 0:
            return 0.0

        return (expenses / revenue) * 100

    def _calculate_budget_variance(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate budget variance based on project budgets vs actual costs"""
        # Use project budget data as a proxy for overall budget variance
        projects_with_budget = Project.objects.filter(
            created_at__lte=end_date,
            budget__isnull=False,
            actual_cost__isnull=False
        )

        if not projects_with_budget.exists():
            return 0.0

        total_budget = 0
        total_actual = 0

        for project in projects_with_budget:
            total_budget += float(project.budget)
            total_actual += float(project.actual_cost)

        if total_budget == 0:
            return 0.0

        # Calculate variance as percentage
        variance = ((total_actual - total_budget) / total_budget) * 100
        return variance

    def _calculate_roi(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate return on investment"""
        revenue = self._calculate_monthly_revenue(start_date, end_date)

        expenses = Expense.objects.filter(
            expense_date__range=[start_date.date(), end_date.date()]
        ).aggregate(total=Sum('amount'))['total']

        expenses = float(expenses) if expenses else 0.0

        if expenses == 0:
            return 0.0

        profit = revenue - expenses
        return (profit / expenses) * 100

    # ==================== OPERATIONS KPI CALCULATIONS ====================

    def _calculate_project_completion_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate percentage of projects completed on time"""
        relevant_projects = Project.objects.filter(
            Q(end_date__range=[start_date.date(), end_date.date()]) |
            Q(actual_end_date__range=[start_date.date(), end_date.date()])
        ).distinct()

        if not relevant_projects.exists():
            return 0.0

        completed_on_time = relevant_projects.filter(
            status='COMPLETED',
            actual_end_date__isnull=False,
            actual_end_date__lte=F('end_date')
        ).count()

        total_projects = relevant_projects.count()
        return (completed_on_time / total_projects) * 100 if total_projects > 0 else 0.0

    def _calculate_task_completion_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate task completion rate as percentage"""
        tasks_in_period = Task.objects.filter(
            created_at__lte=end_date
        )

        if not tasks_in_period.exists():
            return 0.0

        completed_tasks = tasks_in_period.filter(status='COMPLETED').count()
        total_tasks = tasks_in_period.count()

        return (completed_tasks / total_tasks) * 100 if total_tasks > 0 else 0.0

    def _calculate_average_project_duration(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate average project duration in days"""
        completed_projects = Project.objects.filter(
            status='COMPLETED',
            actual_end_date__isnull=False,
            actual_end_date__range=[start_date.date(), end_date.date()]
        )

        if not completed_projects.exists():
            return 0.0

        total_duration = 0
        project_count = 0

        for project in completed_projects:
            if project.start_date and project.actual_end_date:
                duration = (project.actual_end_date - project.start_date).days
                total_duration += duration
                project_count += 1

        return total_duration / project_count if project_count > 0 else 0.0

    def _calculate_project_budget_variance(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate project budget variance as percentage"""
        projects = Project.objects.filter(
            end_date__range=[start_date.date(), end_date.date()],
            budget__isnull=False,
            actual_cost__isnull=False
        )

        if not projects.exists():
            return 0.0

        total_variance = 0
        project_count = 0

        for project in projects:
            if project.budget and project.actual_cost:
                variance = ((float(project.actual_cost) - float(project.budget)) / float(project.budget)) * 100
                total_variance += variance
                project_count += 1

        return total_variance / project_count if project_count > 0 else 0.0

    def _calculate_resource_utilization(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate resource utilization rate"""
        total_employees = Employee.objects.filter(is_active=True).count()

        if total_employees == 0:
            return 0.0

        # Count employees assigned to active tasks
        assigned_employees = Task.objects.filter(
            status__in=['IN_PROGRESS', 'PENDING'],
            assigned_to__isnull=False
        ).values('assigned_to').distinct().count()

        return (assigned_employees / total_employees) * 100

    def _calculate_on_time_delivery(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate on-time delivery rate for projects"""
        return self._calculate_project_completion_rate(start_date, end_date)

    def _calculate_project_success_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate project success rate (completed vs total)"""
        projects = Project.objects.filter(
            created_at__lte=end_date
        )

        if not projects.exists():
            return 0.0

        successful_projects = projects.filter(status='COMPLETED').count()
        total_projects = projects.count()

        return (successful_projects / total_projects) * 100

    def _calculate_capacity_utilization(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate capacity utilization based on task assignments"""
        return self._calculate_resource_utilization(start_date, end_date)

    # ==================== CUSTOMER KPI CALCULATIONS ====================

    def _calculate_customer_acquisition_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate customer acquisition rate based on new invoices"""
        # Count unique customers with invoices in the period
        new_customers = CustomerInvoice.objects.filter(
            invoice_date__range=[start_date.date(), end_date.date()]
        ).values('customer_id').distinct().count()

        # Calculate rate per month
        period_months = max(1, (end_date - start_date).days / 30)
        return new_customers / period_months

    def _calculate_customer_retention_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate customer retention rate"""
        # Get customers from previous period
        period_length = end_date - start_date
        prev_start = start_date - period_length

        prev_customers = set(CustomerInvoice.objects.filter(
            invoice_date__range=[prev_start.date(), start_date.date()]
        ).values_list('customer_id', flat=True))

        current_customers = set(CustomerInvoice.objects.filter(
            invoice_date__range=[start_date.date(), end_date.date()]
        ).values_list('customer_id', flat=True))

        if not prev_customers:
            return 0.0

        retained_customers = len(prev_customers.intersection(current_customers))
        return (retained_customers / len(prev_customers)) * 100

    def _calculate_average_deal_size(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate average deal size from invoices"""
        return self._calculate_average_invoice_value(start_date, end_date)

    def _calculate_customer_lifetime_value(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate customer lifetime value - simplified calculation"""
        avg_invoice = self._calculate_average_invoice_value(start_date, end_date)
        retention_rate = self._calculate_customer_retention_rate(start_date, end_date) / 100

        if retention_rate == 0:
            return avg_invoice

        # Simplified CLV calculation
        return avg_invoice * (retention_rate / (1 - retention_rate))

    def _calculate_collection_time(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate average invoice collection time in days"""
        paid_invoices = CustomerInvoice.objects.filter(
            status='PAID',
            invoice_date__range=[start_date.date(), end_date.date()],
            payment_date__isnull=False
        )

        if not paid_invoices.exists():
            return 0.0

        total_days = 0
        invoice_count = 0

        for invoice in paid_invoices:
            if invoice.payment_date and invoice.invoice_date:
                days = (invoice.payment_date - invoice.invoice_date).days
                total_days += days
                invoice_count += 1

        return total_days / invoice_count if invoice_count > 0 else 0.0

    # ==================== ASSET & COMPLIANCE KPI CALCULATIONS ====================

    def _calculate_asset_utilization(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate asset utilization rate"""
        try:
            total_assets = Asset.objects.filter(is_active=True).count()

            if total_assets == 0:
                return 0.0

            # Count assets that had maintenance or were used in projects
            utilized_assets = Asset.objects.filter(
                Q(assetmaintenance__maintenance_date__range=[start_date.date(), end_date.date()]) |
                Q(project__start_date__lte=end_date.date(), project__end_date__gte=start_date.date())
            ).distinct().count()

            return (utilized_assets / total_assets) * 100
        except Exception as e:
            logger.warning(f"Asset model not available for utilization calculation: {e}")
            return 0.0  # Return 0 if Asset model doesn't exist

    def _calculate_maintenance_cost_ratio(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate maintenance cost as percentage of asset value"""
        try:
            total_asset_value = Asset.objects.filter(is_active=True).aggregate(
                total=Sum('purchase_price')
            )['total']

            maintenance_costs = AssetMaintenance.objects.filter(
                maintenance_date__range=[start_date.date(), end_date.date()]
            ).aggregate(total=Sum('cost'))['total']

            if not total_asset_value or total_asset_value == 0:
                return 0.0

            maintenance_costs = float(maintenance_costs) if maintenance_costs else 0.0
            return (maintenance_costs / float(total_asset_value)) * 100
        except Exception as e:
            logger.warning(f"Asset maintenance data not available: {e}")
            return 0.0  # Return 0 if maintenance data doesn't exist

    def _calculate_compliance_score(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate compliance score based on controls and violations"""
        try:
            total_controls = ComplianceControl.objects.filter(is_active=True).count()

            if total_controls == 0:
                return 100.0

            # Count controls that are compliant
            compliant_controls = ComplianceControl.objects.filter(
                is_active=True,
                status='COMPLIANT'
            ).count()

            return (compliant_controls / total_controls) * 100
        except Exception as e:
            logger.warning(f"Compliance data not available: {e}")
            return 0.0  # Return 0 if compliance data doesn't exist

    def _calculate_security_incident_rate(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate security incident rate per month"""
        try:
            incidents = SecurityAlert.objects.filter(
                created_at__range=[start_date, end_date],
                severity__in=['HIGH', 'CRITICAL']
            ).count()

            # Calculate rate per month
            period_months = max(1, (end_date - start_date).days / 30)
            return incidents / period_months
        except Exception as e:
            logger.warning(f"Security incident data not available: {e}")
            return 0.0  # Return 0 if security data doesn't exist

    def _calculate_system_uptime(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate system uptime percentage based on system activity"""
        # Calculate uptime based on system activity (API calls, user logins, etc.)
        try:
            # Use audit logs or user activity as a proxy for system uptime
            from django.contrib.auth.models import User

            # Count days with user activity as "up" days
            total_days = (end_date - start_date).days + 1

            # Get unique days with user login activity
            active_days = User.objects.filter(
                last_login__range=[start_date, end_date]
            ).dates('last_login', 'day').count()

            if total_days == 0:
                return 100.0

            uptime_percentage = (active_days / total_days) * 100
            return min(uptime_percentage, 100.0)

        except Exception as e:
            logger.warning(f"System uptime calculation failed: {e}")
            return 0.0


# Global instance of the enhanced engine
enhanced_kpi_engine = EnhancedKPICalculationEngine()
