/**
 * HR Analytics Dashboard Page
 * HR-focused KPI dashboard with employee metrics and organizational insights
 */

import React from 'react'
import { useLocation } from 'react-router-dom'
import HierarchicalKPIDashboard from '../../components/kpi/HierarchicalKPIDashboard'

interface HRAnalyticsDashboardProps {
  kpiType?: 'performance' | 'retention' | 'attendance' | 'satisfaction' | 'compliance' | 'general'
}

const HRAnalyticsDashboard: React.FC<HRAnalyticsDashboardProps> = ({ kpiType }) => {
  const location = useLocation()
  // Default to Arabic for now
  const language = 'ar'

  // Determine KPI type from URL if not provided as prop
  const currentKpiType = kpiType || (() => {
    const path = location.pathname
    if (path.includes('/performance')) return 'performance'
    if (path.includes('/retention')) return 'retention'
    if (path.includes('/attendance')) return 'attendance'
    if (path.includes('/satisfaction')) return 'satisfaction'
    if (path.includes('/compliance')) return 'compliance'
    return 'general'
  })()

  // Configure dashboard based on KPI type
  const getDashboardConfig = () => {
    switch (currentKpiType) {
      case 'performance':
        return {
          title: 'HR Performance Analytics',
          titleAr: 'تحليلات أداء الموارد البشرية',
          categories: ['HR', 'Performance', 'Productivity'],
          color: 'from-blue-500 to-indigo-600'
        }
      case 'retention':
        return {
          title: 'Employee Retention Analytics',
          titleAr: 'تحليلات الاحتفاظ بالموظفين',
          categories: ['HR', 'Retention', 'Turnover'],
          color: 'from-green-500 to-emerald-600'
        }
      case 'attendance':
        return {
          title: 'Attendance Analytics',
          titleAr: 'تحليلات الحضور',
          categories: ['HR', 'Attendance', 'Time'],
          color: 'from-purple-500 to-violet-600'
        }
      case 'satisfaction':
        return {
          title: 'Employee Satisfaction Analytics',
          titleAr: 'تحليلات رضا الموظفين',
          categories: ['HR', 'Satisfaction', 'Engagement'],
          color: 'from-yellow-500 to-orange-600'
        }
      case 'compliance':
        return {
          title: 'HR Compliance Analytics',
          titleAr: 'تحليلات الامتثال للموارد البشرية',
          categories: ['HR', 'Compliance', 'Legal'],
          color: 'from-red-500 to-pink-600'
        }
      default:
        return {
          title: 'HR Analytics Dashboard',
          titleAr: 'لوحة تحليلات الموارد البشرية',
          categories: ['HR', 'Employee', 'Retention', 'Satisfaction'],
          color: 'from-green-500 to-teal-600'
        }
    }
  }

  const config = getDashboardConfig()

  // Get specific KPIs for each type (using actual database names)
  const getSpecificKPIsForType = (type: string): string[] => {
    switch (type) {
      case 'performance':
        return [
          'Employee Productivity Score',
          'Task Completion Rate',
          'Training Hours per Employee'
        ]
      case 'retention':
        return [
          'Employee Turnover Rate',
          'Employee Retention Rate'
        ]
      case 'attendance':
        return [
          'Average Attendance Rate'
        ]
      case 'satisfaction':
        return [
          'Employee Satisfaction Score',
          'Employee Satisfaction'
        ]
      case 'compliance':
        return [
          'Training Hours per Employee'
        ]
      default:
        return [
          'Average Attendance Rate',
          'Employee Turnover Rate',
          'Employee Productivity Score',
          'Employee Satisfaction Score'
        ]
    }
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-teal-900 p-6`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Dashboard Header */}
      <div className="mb-6">
        <div className={`mb-4 p-6 bg-gradient-to-r ${config.color} backdrop-blur-sm rounded-lg border border-white/20 shadow-xl`}>
          <h1 className="text-3xl font-bold text-white mb-2">
            {language === 'ar' ? config.titleAr : config.title}
          </h1>
          <p className="text-white/80 text-lg">
            {language === 'ar'
              ? `📊 نوع المؤشر: ${currentKpiType} | الفئات: ${config.categories.join('، ')} | الدور: مدير الموارد البشرية`
              : `🎯 KPI Type: ${currentKpiType} | Categories: ${config.categories.join(', ')} | Role: HR_MANAGER`
            }
          </p>
        </div>
      </div>

      <HierarchicalKPIDashboard
        dashboardType="hr"
        className="max-w-7xl mx-auto"
        kpiFilters={{
          categories: config.categories,
          kpiType: currentKpiType,
          specificKPIs: getSpecificKPIsForType(currentKpiType)
        }}
      />
    </div>
  )
}

export default HRAnalyticsDashboard
