/**
 * Financial Analytics Dashboard Page
 * Finance-focused KPI dashboard with revenue, profitability, and budget metrics
 */

import React from 'react'
import { useLocation } from 'react-router-dom'
import HierarchicalKPIDashboard from '../../components/kpi/HierarchicalKPIDashboard'

interface FinancialAnalyticsDashboardProps {
  kpiType?: 'revenue' | 'profitability' | 'cashflow' | 'budget' | 'costs' | 'general'
}

const FinancialAnalyticsDashboard: React.FC<FinancialAnalyticsDashboardProps> = ({ kpiType }) => {
  const location = useLocation()

  // Determine KPI type from URL if not provided as prop
  const currentKpiType = kpiType || (() => {
    const path = location.pathname
    if (path.includes('/revenue')) return 'revenue'
    if (path.includes('/profitability')) return 'profitability'
    if (path.includes('/cashflow')) return 'cashflow'
    if (path.includes('/budget')) return 'budget'
    if (path.includes('/costs')) return 'costs'
    return 'general'
  })()

  // Configure dashboard based on KPI type
  const getDashboardConfig = () => {
    switch (currentKpiType) {
      case 'revenue':
        return {
          title: 'Revenue Analytics',
          titleAr: 'تحليلات الإيرادات',
          categories: ['Financial', 'Revenue', 'Sales'],
          color: 'from-green-500 to-emerald-600'
        }
      case 'profitability':
        return {
          title: 'Profitability Analytics',
          titleAr: 'تحليلات الربحية',
          categories: ['Financial', 'Profitability', 'Margin'],
          color: 'from-blue-500 to-indigo-600'
        }
      case 'cashflow':
        return {
          title: 'Cash Flow Analytics',
          titleAr: 'تحليلات التدفق النقدي',
          categories: ['Financial', 'CashFlow', 'Liquidity'],
          color: 'from-purple-500 to-violet-600'
        }
      case 'budget':
        return {
          title: 'Budget Analytics',
          titleAr: 'تحليلات الميزانية',
          categories: ['Financial', 'Budget', 'Planning'],
          color: 'from-yellow-500 to-orange-600'
        }
      case 'costs':
        return {
          title: 'Cost Analytics',
          titleAr: 'تحليلات التكاليف',
          categories: ['Financial', 'Cost', 'Expense'],
          color: 'from-red-500 to-pink-600'
        }
      default:
        return {
          title: 'Financial Analytics Dashboard',
          titleAr: 'لوحة التحليلات المالية',
          categories: ['Financial', 'Revenue', 'Cost', 'Budget'],
          color: 'from-yellow-500 to-orange-600'
        }
    }
  }

  const config = getDashboardConfig()

  // Get specific KPIs for each financial type (using actual database names)
  const getSpecificKPIsForType = (type: string): string[] => {
    switch (type) {
      case 'revenue':
        return [
          'Monthly Revenue',
          'Revenue Growth Rate'
        ]
      case 'profitability':
        return [
          'Profit Margin'
        ]
      case 'cashflow':
        return [
          'Cash Flow'
        ]
      case 'budget':
        return [
          'Monthly Revenue',
          'Profit Margin'
        ]
      case 'costs':
        return [
          'Average Invoice Value'
        ]
      default:
        return [
          'Monthly Revenue',
          'Profit Margin',
          'Cash Flow',
          'Revenue Growth Rate'
        ]
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-yellow-900 to-orange-900 p-6">
      {/* Dashboard Header */}
      <div className="mb-6">
        <div className="mb-4 p-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
          <h1 className="text-2xl font-bold text-white mb-2">{config.title}</h1>
          <p className="text-white/70 text-sm">
            💰 KPI Type: {currentKpiType} | Categories: {config.categories.join(', ')} | Role: FINANCE_MANAGER
          </p>
        </div>
      </div>

      <HierarchicalKPIDashboard
        dashboardType="financial"
        className="max-w-7xl mx-auto"
        kpiFilters={{
          categories: config.categories,
          kpiType: currentKpiType,
          specificKPIs: getSpecificKPIsForType(currentKpiType)
        }}
      />
    </div>
  )
}

export default FinancialAnalyticsDashboard
