/**
 * Department Analytics Dashboard Page
 * Department-focused KPI dashboard with team performance and productivity metrics
 */

import React from 'react'
import { useLocation } from 'react-router-dom'
import HierarchicalKPIDashboard from '../../components/kpi/HierarchicalKPIDashboard'

interface DepartmentAnalyticsDashboardProps {
  kpiType?: 'productivity' | 'team' | 'resource' | 'performance' | 'general'
}

const DepartmentAnalyticsDashboard: React.FC<DepartmentAnalyticsDashboardProps> = ({ kpiType }) => {
  const location = useLocation()

  // Determine KPI type from URL if not provided as prop
  const currentKpiType = kpiType || (() => {
    const path = location.pathname
    if (path.includes('/productivity')) return 'productivity'
    if (path.includes('/team')) return 'team'
    if (path.includes('/resource')) return 'resource'
    if (path.includes('/performance')) return 'performance'
    return 'general'
  })()

  // Configure dashboard based on KPI type
  const getDashboardConfig = () => {
    switch (currentKpiType) {
      case 'productivity':
        return {
          title: 'Department Productivity Analytics',
          titleAr: 'تحليلات إنتاجية القسم',
          categories: ['Department', 'Productivity', 'Efficiency'],
          color: 'from-blue-500 to-indigo-600'
        }
      case 'team':
        return {
          title: 'Team Performance Analytics',
          titleAr: 'تحليلات أداء الفريق',
          categories: ['Department', 'Team', 'Collaboration'],
          color: 'from-green-500 to-emerald-600'
        }
      case 'resource':
        return {
          title: 'Resource Utilization Analytics',
          titleAr: 'تحليلات استخدام الموارد',
          categories: ['Department', 'Resource', 'Utilization'],
          color: 'from-purple-500 to-violet-600'
        }
      case 'performance':
        return {
          title: 'Department Performance Analytics',
          titleAr: 'تحليلات أداء القسم',
          categories: ['Department', 'Performance', 'KPI'],
          color: 'from-yellow-500 to-orange-600'
        }
      default:
        return {
          title: 'Department Analytics Dashboard',
          titleAr: 'لوحة تحليلات القسم',
          categories: ['Department', 'Team', 'Productivity'],
          color: 'from-blue-500 to-indigo-600'
        }
    }
  }

  const config = getDashboardConfig()

  // Get specific KPIs for each department type (using actual database names)
  const getSpecificKPIsForType = (type: string): string[] => {
    switch (type) {
      case 'productivity':
        return [
          'Employee Productivity Score',
          'Task Completion Rate',
          'Process Efficiency'
        ]
      case 'team':
        return [
          'Project Completion Rate',
          'Average Project Duration'
        ]
      case 'resource':
        return [
          'Resource Utilization Rate',
          'System Uptime'
        ]
      case 'performance':
        return [
          'Employee Productivity Score',
          'Project Completion Rate',
          'Task Completion Rate'
        ]
      default:
        return [
          'Employee Productivity Score',
          'Resource Utilization Rate',
          'Project Completion Rate',
          'Task Completion Rate'
        ]
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 p-6">
      {/* Dashboard Header */}
      <div className="mb-6">
        <div className="mb-4 p-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
          <h1 className="text-2xl font-bold text-white mb-2">{config.title}</h1>
          <p className="text-white/70 text-sm">
            🏢 KPI Type: {currentKpiType} | Categories: {config.categories.join(', ')} | Role: DEPARTMENT_MANAGER
          </p>
        </div>
      </div>

      <HierarchicalKPIDashboard
        dashboardType="department"
        className="max-w-7xl mx-auto"
        kpiFilters={{
          categories: config.categories,
          kpiType: currentKpiType,
          specificKPIs: getSpecificKPIsForType(currentKpiType)
        }}
      />
    </div>
  )
}

export default DepartmentAnalyticsDashboard
