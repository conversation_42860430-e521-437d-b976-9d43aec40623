import React, { useState, useEffect } from 'react'
import {
  Server,
  Database,
  Shield,
  Activity,
  AlertTriangle,
  CheckCircle,
  Settings,
  Users,
  RefreshCw,
  HardDrive,
  Cpu,
  MemoryStick,
  Network,
  Globe,
  Key,
  FileText,
  Clock,
  TrendingUp,
  BarChart3,
  Zap,
  Monitor,
  Terminal,
  Gauge,
  Power,
  Wifi
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'

interface SuperAdminDashboardProps {
  language?: 'ar' | 'en'
}

interface SystemMetrics {
  uptime: string
  cpuUsage: number
  memoryUsage: number
  diskUsage: number
  activeUsers: number
  totalUsers: number
  systemHealth: 'excellent' | 'good' | 'warning' | 'critical'
  securityScore: number
  activeThreats: number
  lastUpdated: Date | null
  loading: boolean
}

export default function SuperAdminDashboard({ language = 'en' }: SuperAdminDashboardProps) {
  const isRTL = language === 'ar'
  
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics>({
    uptime: 'Loading...',
    cpuUsage: 0,
    memoryUsage: 0,
    diskUsage: 0,
    activeUsers: 0,
    totalUsers: 0,
    systemHealth: 'good',
    securityScore: 0,
    activeThreats: 0,
    lastUpdated: null,
    loading: true
  })

  const t = {
    systemAdministration: isRTL ? 'إدارة النظام' : 'System Administration',
    systemOverview: isRTL ? 'نظرة عامة على النظام' : 'System Overview',
    refresh: isRTL ? 'تحديث' : 'Refresh',
    systemSettings: isRTL ? 'إعدادات النظام' : 'System Settings',
    systemHealth: isRTL ? 'صحة النظام' : 'System Health',
    uptime: isRTL ? 'وقت التشغيل' : 'Uptime',
    cpuUsage: isRTL ? 'استخدام المعالج' : 'CPU Usage',
    memoryUsage: isRTL ? 'استخدام الذاكرة' : 'Memory Usage',
    diskUsage: isRTL ? 'استخدام القرص' : 'Disk Usage',
    userActivity: isRTL ? 'نشاط المستخدمين' : 'User Activity',
    activeUsers: isRTL ? 'المستخدمون النشطون' : 'Active Users',
    totalUsers: isRTL ? 'إجمالي المستخدمين' : 'Total Users',
    securityStatus: isRTL ? 'حالة الأمان' : 'Security Status',
    securityScore: isRTL ? 'نقاط الأمان' : 'Security Score',
    activeThreats: isRTL ? 'التهديدات النشطة' : 'Active Threats',
    excellent: isRTL ? 'ممتاز' : 'Excellent',
    good: isRTL ? 'جيد' : 'Good',
    warning: isRTL ? 'تحذير' : 'Warning',
    critical: isRTL ? 'حرج' : 'Critical'
  }

  useEffect(() => {
    // Simulate loading system metrics
    const loadMetrics = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        setSystemMetrics({
          uptime: '15 days, 4 hours',
          cpuUsage: 45,
          memoryUsage: 68,
          diskUsage: 32,
          activeUsers: 127,
          totalUsers: 1543,
          systemHealth: 'good',
          securityScore: 85,
          activeThreats: 0,
          lastUpdated: new Date(),
          loading: false
        })
      } catch (error) {
        console.error('Failed to load system metrics:', error)
        setSystemMetrics(prev => ({ ...prev, loading: false }))
      }
    }

    loadMetrics()
  }, [])

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent': return 'text-green-400'
      case 'good': return 'text-blue-400'
      case 'warning': return 'text-yellow-400'
      case 'critical': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getHealthBadgeColor = (health: string) => {
    switch (health) {
      case 'excellent': return 'bg-green-500'
      case 'good': return 'bg-blue-500'
      case 'warning': return 'bg-yellow-500'
      case 'critical': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">{t.systemAdministration}</h1>
              <p className="text-white/70">{t.systemOverview}</p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                <RefreshCw className="h-4 w-4 mr-2" />
                {t.refresh}
              </Button>
              <Button className="bg-blue-500 hover:bg-blue-600 text-white">
                <Settings className="h-4 w-4 mr-2" />
                {t.systemSettings}
              </Button>
            </div>
          </div>
        </div>

        {/* System Health Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center gap-2">
                <Activity className="h-5 w-5" />
                {t.systemHealth}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <Badge className={getHealthBadgeColor(systemMetrics.systemHealth)}>
                  {t[systemMetrics.systemHealth as keyof typeof t] || systemMetrics.systemHealth}
                </Badge>
                <CheckCircle className={`h-6 w-6 ${getHealthColor(systemMetrics.systemHealth)}`} />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center gap-2">
                <Clock className="h-5 w-5" />
                {t.uptime}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-white">
                {systemMetrics.loading ? 'Loading...' : systemMetrics.uptime}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center gap-2">
                <Users className="h-5 w-5" />
                {t.activeUsers}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-white">
                {systemMetrics.loading ? 'Loading...' : `${systemMetrics.activeUsers}/${systemMetrics.totalUsers}`}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center gap-2">
                <Shield className="h-5 w-5" />
                {t.securityScore}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <p className="text-2xl font-bold text-white">
                  {systemMetrics.loading ? 'Loading...' : `${systemMetrics.securityScore}%`}
                </p>
                <Badge className="bg-green-500">
                  {systemMetrics.activeThreats} {t.activeThreats}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* System Resources */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Cpu className="h-5 w-5" />
                {t.cpuUsage}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-white/70">Current</span>
                <span className="text-white font-semibold">
                  {systemMetrics.loading ? 'Loading...' : `${systemMetrics.cpuUsage}%`}
                </span>
              </div>
              <Progress value={systemMetrics.cpuUsage} className="h-2" />
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <MemoryStick className="h-5 w-5" />
                {t.memoryUsage}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-white/70">Current</span>
                <span className="text-white font-semibold">
                  {systemMetrics.loading ? 'Loading...' : `${systemMetrics.memoryUsage}%`}
                </span>
              </div>
              <Progress value={systemMetrics.memoryUsage} className="h-2" />
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <HardDrive className="h-5 w-5" />
                {t.diskUsage}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-white/70">Current</span>
                <span className="text-white font-semibold">
                  {systemMetrics.loading ? 'Loading...' : `${systemMetrics.diskUsage}%`}
                </span>
              </div>
              <Progress value={systemMetrics.diskUsage} className="h-2" />
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button className="bg-blue-500/20 border border-blue-500/30 text-blue-300 hover:bg-blue-500/30 h-20 flex-col">
            <Monitor className="h-6 w-6 mb-2" />
            System Monitor
          </Button>
          <Button className="bg-green-500/20 border border-green-500/30 text-green-300 hover:bg-green-500/30 h-20 flex-col">
            <Database className="h-6 w-6 mb-2" />
            Database
          </Button>
          <Button className="bg-purple-500/20 border border-purple-500/30 text-purple-300 hover:bg-purple-500/30 h-20 flex-col">
            <Network className="h-6 w-6 mb-2" />
            Network
          </Button>
          <Button className="bg-orange-500/20 border border-orange-500/30 text-orange-300 hover:bg-orange-500/30 h-20 flex-col">
            <Terminal className="h-6 w-6 mb-2" />
            Terminal
          </Button>
        </div>
      </div>
    </div>
  )
}
