/**
 * Consolidated KPI Service
 *
 * PRIMARY KPI SERVICE - Use this for all KPI operations
 *
 * Features:
 * - Hierarchical access control
 * - Role-based filtering
 * - Real-time updates
 * - Arabic language support
 * - Enterprise automation
 *
 * Replaces:
 * - Legacy KPI service methods
 * - Duplicate algorithm services
 * - Manual KPI entry (now automated)
 */

import { apiClient } from './api'

export interface KPICategory {
  id: string
  name: string
  name_ar: string
  description: string
  description_ar: string
  icon: string
  color: string
  is_active: boolean
  sort_order: number
  kpi_count: number
  created_at: string
  updated_at: string
}

export interface KPI {
  id: string
  name: string
  name_ar: string
  description: string
  description_ar: string
  category: string
  category_name: string
  category_name_ar: string
  measurement_type: 'NUMBER' | 'PERCENTAGE' | 'CURRENCY' | 'RATIO' | 'SCORE' | 'TIME'
  unit: string
  unit_ar: string
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY'
  trend_direction: 'UP' | 'DOWN' | 'TARGET'
  formula: string
  data_source: string
  calculation_method: string
  target_value: number | null
  warning_threshold: number | null
  critical_threshold: number | null
  visible_to_roles: string[]
  owner: string | null
  owner_name: string
  status: 'ACTIVE' | 'INACTIVE' | 'DRAFT' | 'ARCHIVED'
  is_automated: boolean
  automation_config: Record<string, any>
  created_by: string
  created_by_name: string
  created_at: string
  updated_at: string
  current_value: {
    value: number
    period_start: string
    period_end: string
    recorded_at: string
  } | null
  target_achievement: number | null
  trend: {
    change_percentage: number
    direction: 'up' | 'down' | 'stable'
  } | null
}

export interface KPIValue {
  id: string
  kpi: string
  kpi_name: string
  kpi_unit: string
  value: number
  period_start: string
  period_end: string
  department: string | null
  department_name: string
  project: string | null
  project_name: string
  employee: string | null
  employee_name: string
  data_quality_score: number
  confidence_level: number
  notes: string
  recorded_by: string
  recorded_by_name: string
  recorded_at: string
  is_estimated: boolean
  data_source?: string // Optional data source field
  source_data: Record<string, any>
}

export interface KPITarget {
  id: string
  kpi: string
  kpi_id?: string // Optional for backward compatibility
  kpi_name: string
  target_value: number
  target_type: 'ABSOLUTE' | 'PERCENTAGE_CHANGE' | 'RELATIVE'
  start_date: string
  end_date: string
  department: string | null
  department_name: string
  project: string | null
  project_name: string
  employee: string | null
  employee_name: string
  description: string
  is_stretch_goal: boolean
  priority?: string // Optional priority field
  is_active?: boolean // Optional active status
  weight: number
  created_by: string
  created_by_name: string
  created_at: string
  updated_at: string
}

export interface KPIAlert {
  id: string
  kpi: string
  kpi_name: string
  alert_type: 'THRESHOLD_BREACH' | 'TARGET_MISSED' | 'TREND_CHANGE' | 'DATA_QUALITY' | 'MISSING_DATA'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  title: string
  title_ar: string
  message: string
  message_ar: string
  current_value: number | null
  threshold_value: number | null
  target_value: number | null
  status: 'ACTIVE' | 'ACKNOWLEDGED' | 'RESOLVED' | 'DISMISSED'
  acknowledged_by: string | null
  acknowledged_by_name: string
  acknowledged_at: string | null
  resolved_by: string | null
  resolved_by_name: string
  resolved_at: string | null
  resolution_notes: string
  notified_users: string[]
  notification_sent: boolean
  created_at: string
  updated_at: string
}

export interface KPIDashboard {
  categories: KPICategory[]
  recent_alerts: KPIAlert[]
  top_performing_kpis: KPI[]
  underperforming_kpis: KPI[]
  kpi_summary: {
    total_kpis: number
    active_kpis: number
    kpis_on_target: number
    kpis_above_target: number
    kpis_below_target: number
    active_alerts: number
    critical_alerts: number
    categories_count: number
    last_updated: string
  }
}

export interface KPITrendData {
  kpi_id: string
  kpi_name: string
  data_points: Array<{
    date: string
    value: number
    period_start: string
    period_end: string
  }>
  trend_direction: 'up' | 'down' | 'stable'
  change_percentage: number
}

class KPIService {
  // Mock data mode removed - service now only uses real API data

  // Method to check if backend is available
  async checkBackendConnection(): Promise<boolean> {
    try {
      const response = await apiClient.get('/kpi/categories/')
      return response.status === 200
    } catch (error) {
      console.warn('Backend connection failed:', error)
      return false
    }
  }

  // Categories
  async getCategories(): Promise<KPICategory[]> {
    try {
      const response = await apiClient.get<KPICategory[]>('/kpi/categories/')
      return response.data as KPICategory[]
    } catch (error) {
      console.error('Error fetching KPI categories:', error)
      // No more mock data fallbacks - return empty array if API fails
      return []
    }
  }



  async createCategory(data: Partial<KPICategory>): Promise<KPICategory> {
    try {
      const response = await apiClient.post('/kpi/categories/', data)
      return response.data
    } catch (error) {
      console.error('Error creating category:', error)
      throw error
    }
  }

  async updateCategory(id: string, data: Partial<KPICategory>): Promise<KPICategory> {
    try {
      const response = await apiClient.put(`/kpi/categories/${id}/`, data)
      return response.data
    } catch (error) {
      console.error('Error updating category:', error)
      throw error
    }
  }

  async deleteCategory(id: string): Promise<void> {
    try {
      await apiClient.delete(`/kpi/categories/${id}/`)
    } catch (error) {
      console.error('Error deleting category:', error)
      // For development, just log the deletion
      console.log(`Mock: Category ${id} deleted successfully`)
    }
  }

  async getCategoryKPIs(categoryId: string): Promise<KPI[]> {
    const response = await apiClient.get(`/kpi/categories/${categoryId}/kpis/`)
    return response.data as KPI[]
  }

  // KPIs
  async getKPIs(params?: {
    status?: string
    category?: string
    limit?: number
    offset?: number
    categories?: string[]
    role_based?: boolean
    fallback_mode?: boolean
    role?: string
    departments?: number[]
    projects?: number[]
    employees?: number[]
    dashboard_type?: string
    category_filter?: string
    specific_kpis?: string
    kpi_type?: string
  }): Promise<{ success: boolean; data?: KPI[]; error?: string }> {
    try {
      // Use enhanced KPI endpoint for hierarchical filtering
      const endpoint = params?.role_based || params?.categories || params?.fallback_mode
        ? '/kpi/enhanced/kpis/'
        : '/kpi/kpis/'

      // Clean up params for API call
      const cleanParams: Record<string, string | number | boolean> = {}

      // Copy valid params
      if (params?.status) cleanParams.status = params.status
      if (params?.category) cleanParams.category = params.category
      if (params?.limit) cleanParams.limit = params.limit
      if (params?.offset) cleanParams.offset = params.offset
      if (params?.role) cleanParams.role = params.role
      if (params?.dashboard_type) cleanParams.dashboard_type = params.dashboard_type
      if (params?.category_filter) cleanParams.category_filter = params.category_filter
      if (params?.specific_kpis) cleanParams.specific_kpis = params.specific_kpis
      if (params?.kpi_type) cleanParams.kpi_type = params.kpi_type

      // Convert categories array to comma-separated string if needed
      if (params?.categories && Array.isArray(params.categories)) {
        cleanParams.category = params.categories.join(',')
      }

      // Convert array params to comma-separated strings
      if (params?.departments && Array.isArray(params.departments)) {
        cleanParams.departments = params.departments.join(',')
      }
      if (params?.projects && Array.isArray(params.projects)) {
        cleanParams.projects = params.projects.join(',')
      }
      if (params?.employees && Array.isArray(params.employees)) {
        cleanParams.employees = params.employees.join(',')
      }

      const response = await apiClient.get(endpoint, { params: cleanParams })

      // Handle paginated response from DRF
      let kpiData: KPI[] = []
      const responseData = response.data as any

      if (Array.isArray(responseData)) {
        // Direct array response
        kpiData = responseData
      } else if (responseData?.results && Array.isArray(responseData.results)) {
        // Paginated response
        kpiData = responseData.results
      } else if (responseData?.data && Array.isArray(responseData.data)) {
        // Nested data structure
        kpiData = responseData.data
      } else {
        console.warn('Unexpected KPI response structure:', responseData)
        kpiData = []
      }

      return {
        success: true,
        data: kpiData
      }
    } catch (error) {
      console.error('Error fetching KPIs:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch KPIs',
        data: []
      }
    }
  }

  // Legacy method for backward compatibility
  async getKPIsLegacy(params?: {
    status?: string
    category?: string
    limit?: number
    offset?: number
  }): Promise<KPI[]> {
    const result = await this.getKPIs(params)
    return result.data || []
  }



  async getKPI(id: string): Promise<KPI> {
    try {
      const response = await apiClient.get(`/kpi/kpis/${id}/`)
      return response.data as KPI
    } catch (error) {
      console.error('Error fetching KPI:', error)
      throw error
    }
  }

  async createKPI(data: Partial<KPI>): Promise<KPI> {
    try {
      const response = await apiClient.post('/kpi/kpis/', data)
      return response.data as KPI
    } catch (error) {
      console.error('Error creating KPI:', error)
      throw error
    }
  }



  async updateKPI(id: string, data: Partial<KPI>): Promise<KPI> {
    try {
      const response = await apiClient.put(`/kpi/kpis/${id}/`, data)
      return response.data as KPI
    } catch (error) {
      console.error('Error updating KPI:', error)
      throw error
    }
  }

  async deleteKPI(id: string): Promise<void> {
    try {
      await apiClient.delete(`/kpi/kpis/${id}/`)
    } catch (error) {
      console.error('Error deleting KPI:', error)
      throw error
    }
  }

  async getKPIValues(kpiId: string, params?: {
    start_date?: string
    end_date?: string
    limit?: number
  }): Promise<KPIValue[]> {
    try {
      const response = await apiClient.get(`/kpi/kpis/${kpiId}/values/`, { params })
      return response.data as KPIValue[]
    } catch (error) {
      console.error('Error fetching KPI values:', error)
      return []
    }
  }



  async getKPITrend(kpiId: string, days: number = 30): Promise<KPITrendData> {
    try {
      const response = await apiClient.get(`/kpi/kpis/${kpiId}/trend/`, { params: { days } })
      return response.data as KPITrendData
    } catch (error) {
      console.error('Error fetching KPI trend:', error)
      // No more mock data fallbacks - return empty trend data if API fails
      return {
        kpi_id: kpiId,
        kpi_name: 'Unknown KPI',
        data_points: [],
        trend_direction: 'stable',
        change_percentage: 0
      }
    }
  }



  async addKPIValue(kpiId: string, data: Partial<KPIValue>): Promise<KPIValue> {
    try {
      // Validate required fields
      if (!data.value && data.value !== 0) {
        throw new Error('Value is required')
      }
      if (!data.period_start) {
        throw new Error('Period start date is required')
      }
      if (!data.period_end) {
        throw new Error('Period end date is required')
      }

      const response = await apiClient.post(`/kpi/kpis/${kpiId}/add_value/`, data)
      return response.data as KPIValue
    } catch (error: any) {
      console.error('Error adding KPI value:', error)

      // Handle specific API errors
      if (error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided')
      } else if (error.response?.status === 404) {
        throw new Error('KPI not found')
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to add values to this KPI')
      }

      throw error
    }
  }

  async updateKPIValue(kpiId: string, valueId: string, data: Partial<KPIValue>): Promise<KPIValue> {
    try {
      const response = await apiClient.put(`/kpi/kpis/${kpiId}/values/${valueId}/`, data)
      return response.data as KPIValue
    } catch (error: any) {
      console.error('Error updating KPI value:', error)

      if (error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided')
      } else if (error.response?.status === 404) {
        throw new Error('KPI value not found')
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to update this KPI value')
      }

      throw error
    }
  }

  async deleteKPIValue(kpiId: string, valueId: string): Promise<void> {
    try {
      await apiClient.delete(`/kpi/kpis/${kpiId}/values/${valueId}/`)
    } catch (error: any) {
      console.error('Error deleting KPI value:', error)

      if (error.response?.status === 404) {
        throw new Error('KPI value not found')
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to delete this KPI value')
      }

      throw error
    }
  }

  // Duplicate function implementations removed - using original implementations above

  // Alias for addKPIValue for backward compatibility
  async addValue(kpiId: string, data: Partial<KPIValue>): Promise<KPIValue> {
    return this.addKPIValue(kpiId, data)
  }

  // Values
  async getValues(params?: {
    kpi?: string
    start_date?: string
    end_date?: string
    limit?: number
    offset?: number
  }): Promise<KPIValue[]> {
    const response = await apiClient.get('/kpi/values/', { params })
    return response.data as KPIValue[]
  }

  async createValue(data: Partial<KPIValue>): Promise<KPIValue> {
    const response = await apiClient.post('/kpi/values/', data)
    return response.data as KPIValue
  }

  async updateValue(id: string, data: Partial<KPIValue>): Promise<KPIValue> {
    const response = await apiClient.put(`/kpi/values/${id}/`, data)
    return response.data as KPIValue
  }

  async deleteValue(id: string): Promise<void> {
    await apiClient.delete(`/kpi/values/${id}/`)
  }

  // Targets
  async getTargets(params?: {
    kpi?: string
    active_only?: boolean
    limit?: number
    offset?: number
  }): Promise<KPITarget[]> {
    try {
      const response = await apiClient.get<KPITarget[]>('/kpi/targets/', { params })
      return response.data as KPITarget[]
    } catch (error) {
      console.error('Error fetching KPI targets:', error)
      // No mock data - return empty array if API fails
      return []
    }
  }



  async createTarget(data: Partial<KPITarget>): Promise<KPITarget> {
    const response = await apiClient.post('/kpi/targets/', data)
    return response.data as KPITarget
  }

  async updateTarget(id: string, data: Partial<KPITarget>): Promise<KPITarget> {
    const response = await apiClient.put(`/kpi/targets/${id}/`, data)
    return response.data as KPITarget
  }

  async deleteTarget(id: string): Promise<void> {
    await apiClient.delete(`/kpi/targets/${id}/`)
  }

  // Alerts
  async getAlerts(params?: {
    status?: string
    severity?: string
    limit?: number
    offset?: number
  }): Promise<KPIAlert[]> {
    try {
      const response = await apiClient.get<KPIAlert[]>('/kpi/alerts/', { params })
      return response.data
    } catch (error) {
      console.error('Error fetching KPI alerts:', error)
      // No mock data - return empty array if API fails
      return []
    }
  }



  async acknowledgeAlert(id: string): Promise<KPIAlert> {
    const response = await apiClient.post(`/kpi/alerts/${id}/acknowledge/`)
    return response.data as KPIAlert
  }

  async resolveAlert(id: string, resolution_notes?: string): Promise<KPIAlert> {
    const response = await apiClient.post(`/kpi/alerts/${id}/resolve/`, { resolution_notes })
    return response.data as KPIAlert
  }

  // Enhanced KPI Methods
  async recalculateKPI(id: string): Promise<KPI> {
    const response = await apiClient.post(`/kpi/enhanced/kpis/${id}/recalculate/`)
    return response.data as KPI
  }

  async recalculateAllKPIs(): Promise<{ message: string; updated_count: number }> {
    const response = await apiClient.post('/kpi/enhanced/kpis/recalculate_all/')
    return response.data as { message: string; updated_count: number }
  }

  // Dashboard
  async getDashboard(params?: {
    start_date?: string
    end_date?: string
    compare_start_date?: string
    compare_end_date?: string
  }): Promise<KPIDashboard> {
    try {
      const response = await apiClient.get<KPIDashboard>('/kpi/dashboard/', { params })
      return response.data as KPIDashboard
    } catch (error) {
      console.error('Error fetching KPI dashboard:', error)
      return {
        categories: [],
        recent_alerts: [],
        top_performing_kpis: [],
        underperforming_kpis: [],
        kpi_summary: {
          total_kpis: 0,
          active_kpis: 0,
          kpis_on_target: 0,
          kpis_above_target: 0,
          kpis_below_target: 0,
          active_alerts: 0,
          critical_alerts: 0,
          categories_count: 0,
          last_updated: new Date().toISOString()
        }
      }
    }
  }
}

export const kpiService = new KPIService()
export default kpiService
